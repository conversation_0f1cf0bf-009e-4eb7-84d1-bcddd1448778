body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #999999;
}

.content-container {
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

h1, h2 {
    margin-top: 0;
    margin-bottom: 15px;
}

.text-line {
    padding: 8px 10px;
    border-bottom: 1px solid #eee;
}

form {
    margin-bottom: 15px;
}

input[type="text"] {
    width: 70%;
    padding: 8px;
}

button {
    padding: 8px 16px;
    background: #4CAF50;
    color: white;
    border: none;
    cursor: pointer;
}

.copy-btn {
    padding: 5px 10px;
    background: #2196F3;
    color: white;
    border: none;
    cursor: pointer;
    margin-right: 5px;
}

.delete-btn {
    padding: 5px 10px;
    background: #f44336;
    color: white;
    border: none;
    cursor: pointer;
}

p {
    margin: 5px 0;
}

.separator-btn {
    padding: 8px 16px;
    background: #4CAF50;
    color: white;
    border: none;
    cursor: pointer;
    margin-left: 5px;
}

.separator-container {
    display: flex;
    align-items: center;
    flex: 1;
    margin-right: 10px;
}

.separator-title {
    padding: 0 10px;
    white-space: nowrap;
    color: #555;
    font-weight: bold;
}

.separator-line {
    height: 1px;
    background-color: #757575;
    margin: 10px 10px 10px 0;
    border: none;
    flex: 1;
}

.text-content {
    margin-right: 10px;
    flex: 1;
    word-break: break-word;
}

.text-actions {
    display: flex;
    gap: 5px;
    flex-shrink: 0;
    align-items: center;
}

.text-line-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.delete-form {
    margin: 0;
}

.input-container {
    display: flex;
    margin-bottom: 10px;
    width: 100%;
}

.input-container input[type="text"] {
    flex: 1;
    margin-right: 10px;
}

.separator-btn-container {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 15px;
}

.separator-btn {
    padding: 8px 16px;
    background: #4CAF50;
    color: white;
    border: none;
    cursor: pointer;
}

.move-form {
    margin: 0;
    display: inline-block;
}

.move-btn {
    padding: 5px 8px;
    background: #9e9e9e;
    color: white;
    border: none;
    cursor: pointer;
    font-weight: bold;
    border-radius: 3px;
}

.move-up-btn {
    background: #2196F3;
}

.move-down-btn {
    background: #2196F3;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.button-group {
    display: flex;
    gap: 10px;
}

.refresh-btn {
    padding: 5px 10px;
    background: #ff9800;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 3px;
}

.refresh-btn:hover {
    background: #f57c00;
}

