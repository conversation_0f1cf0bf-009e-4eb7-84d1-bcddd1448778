// Theme switcher functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('Theme script loaded');
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = document.getElementById('theme-icon');

    console.log('Theme toggle element:', themeToggle);
    console.log('Theme icon element:', themeIcon);

    // Check for server-side theme (from data-theme attribute), then local storage, then system preference
    const htmlElement = document.documentElement;
    const serverTheme = htmlElement.getAttribute('data-theme');
    const savedTheme = serverTheme ||
                      localStorage.getItem('theme') ||
                      (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');

    console.log('Server theme:', serverTheme);
    console.log('Local storage theme:', localStorage.getItem('theme'));
    console.log('Using theme:', savedTheme);

    // Apply the saved theme on page load
    document.documentElement.setAttribute('data-theme', savedTheme);

    // Apply theme class to body
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
    } else {
        document.body.classList.remove('dark-theme');
    }

    // Update toggle position based on current theme
    if (themeToggle) {
        themeToggle.checked = savedTheme === 'dark';
        updateThemeIcon(savedTheme);
    }

    console.log('Initial theme applied:', savedTheme);

    // Listen for toggle changes
    if (themeToggle) {
        themeToggle.addEventListener('change', function(e) {
            const newTheme = e.target.checked ? 'dark' : 'light';

            // Apply theme to HTML element
            document.documentElement.setAttribute('data-theme', newTheme);

            // Apply theme class to body
            if (newTheme === 'dark') {
                document.body.classList.add('dark-theme');
            } else {
                document.body.classList.remove('dark-theme');
            }

            // Save preference locally
            localStorage.setItem('theme', newTheme);

            // Update icon
            updateThemeIcon(newTheme);

            // Save preference on server (for session persistence)
            fetch(`/theme/switch?theme=${newTheme}`)
                .then(response => response.json())
                .then(data => {
                    console.log('Theme saved on server:', data);
                })
                .catch(error => {
                    console.error('Error saving theme on server:', error);
                });

            console.log('Theme switched to:', newTheme);
        });
    }

    // Update the theme icon based on current theme
    function updateThemeIcon(theme) {
        if (themeIcon) {
            if (theme === 'dark') {
                themeIcon.innerHTML = '🌙';
                themeIcon.title = 'Switch to light mode';
            } else {
                themeIcon.innerHTML = '☀️';
                themeIcon.title = 'Switch to dark mode';
            }
        }
    }

    // Add responsive behavior for mobile devices
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarToggler && navbarCollapse) {
        // Close the mobile menu when a link is clicked
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (window.innerWidth < 992) { // Bootstrap lg breakpoint
                    navbarToggler.click();
                }
            });
        });
    }

    // Make tables responsive on small screens
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        const wrapper = document.createElement('div');
        wrapper.classList.add('table-responsive');
        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);
    });

    // Add animation class to elements for smooth theme transition
    const elementsToAnimate = document.querySelectorAll('body, .card, .navbar, .btn, .form-control, .table, footer, .alert');
    elementsToAnimate.forEach(el => {
        el.classList.add('theme-transition');
    });
});
