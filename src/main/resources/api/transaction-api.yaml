openapi: 3.0.3
info:
  title: Transaction Store API
  description: API for managing financial transactions
  version: 1.0.0
  contact:
    name: Transaction Store Team
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: Development server

paths:
  # Account endpoints

  /api/v1/accounts:
    get:
      tags:
        - accounts
      summary: Get all accounts
      description: Retrieve all known accounts
      operationId: getAccounts
      responses:
        '200':
          description: List of accounts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Account'

  # Participants endpoints

  /api/v1/participants:
    get:
      tags:
        - participants
      summary: Get all participants
      description: Retrieve all known participants
      operationId: getParticipants
      responses:
        '200':
          description: List of participants
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Participants'

  # TransactionData endpoints

  /api/v1/transactions:
    get:
      tags:
        - transactions
      summary: Get all transactions
      description: Retrieve all transactions with optional filtering
      operationId: getAllTransactions
      parameters:
        - name: startDate
          in: query
          description: Start date for date range filter (YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
            example: "2024-01-01"
        - name: endDate
          in: query
          description: End date for date range filter (YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
            example: "2024-12-31"
        - name: taxRelevant
          in: query
          description: Filter by tax relevant flag
          required: false
          schema:
            type: boolean
        - name: extraCosts
          in: query
          description: Filter by extra costs flag
          required: false
          schema:
            type: boolean
        - name: accountname
          in: query
          description: Filter by account name
          required: false
          schema:
            type: string
        - name: participant
          in: query
          description: Filter by participant
          required: false
          schema:
            type: string
        - name: referenceid
          in: query
          description: Filter by reference ID
          required: false
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: List of transactions
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransactionData'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      tags:
        - transactions
      summary: Create a new transaction
      description: Create a new transaction data entry
      operationId: createTransaction
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTransactionDataRequest'
      responses:
        '201':
          description: Transaction created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionData'
        '400':
          description: Invalid transaction data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/transactions/{transactionId}:
    get:
      tags:
        - transactions
      summary: Get transaction by ID
      description: Retrieve a specific transaction by its ID
      operationId: getTransactionById
      parameters:
        - name: transactionId
          in: path
          required: true
          description: Transaction ID
          schema:
            type: integer
            format: int64
            example: 12345
      responses:
        '200':
          description: Transaction details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionData'
        '404':
          description: Transaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags:
        - transactions
      summary: Update transaction
      description: Update an existing transaction
      operationId: updateTransaction
      parameters:
        - name: transactionId
          in: path
          required: true
          description: Transaction ID
          schema:
            type: integer
            format: int64
            example: 12345
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTransactionDataRequest'
      responses:
        '200':
          description: Transaction updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionData'
        '404':
          description: Transaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '400':
          description: Invalid transaction data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      tags:
        - transactions
      summary: Delete transaction
      description: Delete a transaction by ID
      operationId: deleteTransaction
      parameters:
        - name: transactionId
          in: path
          required: true
          description: Transaction ID
          schema:
            type: integer
            format: int64
            example: 12345
      responses:
        '204':
          description: Transaction deleted successfully
        '404':
          description: Transaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'


components:
  schemas:
    # Common error response schema
    ErrorResponse:
      type: object
      required:
        - message
        - timestamp
      properties:
        message:
          type: string
          description: Error message
        details:
          type: string
          description: Detailed error information
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
        path:
          type: string
          description: Request path that caused the error

    # Your custom model objects will be added here step by step

    Account:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          maxLength: 150
          description: Account name
          example: "My Savings Account"

    Participants:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          maxLength: 150
          description: Participant name
          example: "John Doe"

    TransactionData:
      type: object
      required:
        - id
        - date
        - accountname
        - amount
        - participant
        - description
      properties:
        id:
          type: integer
          format: int64
          description: Unique transaction identifier
          example: 12345
        date:
          type: string
          format: date
          description: Transaction date
          example: "2024-01-15"
        accountname:
          type: string
          description: Account name
          example: "My Checking Account"
        amount:
          type: integer
          format: int64
          description: Transaction amount in cents
          example: 125050
        participant:
          type: string
          description: Transaction participant
          example: "John Doe"
        description:
          type: string
          maxLength: 200
          description: Transaction description
          example: "Payment for services"
        referenceid:
          type: integer
          format: int64
          description: Optional reference to related transaction
          example: 12344
        taxRelevant:
          type: string
          description: Tax relevant information or classification
          example: "VAT applicable"
        extraCosts:
          type: string
          description: Extra costs description or classification
          example: "Shipping and handling fees"
        asn:
          type: string
          description: Archive serial number - reference to document management archive
          example: "DOC-2024-001234"

    CreateTransactionDataRequest:
      type: object
      required:
        - date
        - accountname
        - amount
        - participant
        - description
      properties:
        date:
          type: string
          format: date
          description: Transaction date
          example: "2024-01-15"
        accountname:
          type: string
          description: Account name
          example: "My Checking Account"
        amount:
          type: integer
          format: int64
          description: Transaction amount in cents
          example: 125050
        participant:
          type: string
          description: Transaction participant
          example: "John Doe"
        description:
          type: string
          maxLength: 200
          description: Transaction description
          example: "Payment for services"
        referenceid:
          type: integer
          format: int64
          description: Optional reference to related transaction
          example: 12344
        taxRelevant:
          type: boolean
          description: Flag to identify tax relevant transactions
          default: false
          example: true
        extraCosts:
          type: boolean
          description: Flag for extra costs
          default: false
          example: false
        asn:
          type: string
          description: Archive serial number - reference to document management archive
          example: "DOC-2024-001234"

    UpdateTransactionDataRequest:
      type: object
      properties:
        date:
          type: string
          format: date
          description: Transaction date
          example: "2024-01-15"
        accountname:
          type: string
          description: Account name
          example: "My Checking Account"
        amount:
          type: integer
          format: int64
          description: Transaction amount in cents
          example: 125050
        participant:
          type: string
          description: Transaction participant
          example: "John Doe"
        description:
          type: string
          maxLength: 200
          description: Transaction description
          example: "Payment for services"
        referenceid:
          type: integer
          format: int64
          description: Optional reference to related transaction
          example: 12344
        taxRelevant:
          type: boolean
          description: Flag to identify tax relevant transactions
          example: true
        extraCosts:
          type: boolean
          description: Flag for extra costs
          example: false
        asn:
          type: string
          description: Archive serial number - reference to document management archive
          example: "DOC-2024-001234"
