<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>LinkServer - Edit Category</title>
</head>
<body>
    <div th:replace="~{layout/main :: body(content=~{::content})}">
        <div th:fragment="content">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3>Edit Category</h3>
                        </div>
                        <div class="card-body">
                            <form th:action="@{/categories/edit/{id}(id=${category.id})}" th:object="${category}" method="post">
                                <input type="hidden" th:field="*{id}">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="name" th:field="*{name}" required>
                                    <div class="text-danger" th:if="${#fields.hasErrors('name')}" th:errors="*{name}"></div>
                                </div>
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" th:field="*{description}" rows="3"></textarea>
                                    <div class="text-danger" th:if="${#fields.hasErrors('description')}" th:errors="*{description}"></div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <a th:href="@{/categories}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">Update Category</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
