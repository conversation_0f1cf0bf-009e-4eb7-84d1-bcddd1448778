<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Content Processor</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #bcb9b9;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 12px;
            box-sizing: border-box;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        input[type="text"]:focus {
            border-color: #4CAF50;
            outline: none;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 20px;
            border: none;
            cursor: pointer;
            border-radius: 4px;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        textarea {
            width: 100%;
            height: 400px;
            padding: 8px;
            box-sizing: border-box;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            border: 2px solid #ddd;
            border-radius: 4px;
            resize: vertical;
        }
        .content-success {
            border-color: #4CAF50;
        }
        .content-error {
            border-color: #f44336;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .loading.htmx-request {
            display: block;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .result-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .result-header {
            margin-bottom: 15px;
        }
        .success-header {
            color: #4CAF50;
            margin: 0 0 10px 0;
        }
        .error-header {
            color: #f44336;
            margin: 0 0 10px 0;
        }
        .processed-url {
            color: #666;
            font-size: 14px;
            margin: 0;
            word-break: break-all;
        }
        .result-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }
        .copy-btn {
            background-color: #2196F3;
        }
        .copy-btn:hover {
            background-color: #1976D2;
        }
        .clear-btn {
            background-color: #ff9800;
        }
        .clear-btn:hover {
            background-color: #f57c00;
        }
        .form-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>🌐 URL Content Processor</h1>

    <div class="form-container">
        <form hx-post="/process-htmx"
              hx-target="#results"
              hx-indicator="#loading"
              hx-swap="innerHTML">
            <div class="form-group">
                <label for="url">Enter URL:</label>
                <input type="text"
                       id="url"
                       name="url"
                       placeholder="https://example.com/article"
                       required>
            </div>
            <button type="submit" id="submit-btn">🚀 Process URL</button>
        </form>
    </div>

    <div id="loading" class="loading">
        <div class="spinner"></div>
        <p>Processing URL... Please wait</p>
    </div>

    <div id="results"></div>

    <script>
        function copyToClipboard() {
            const content = document.getElementById('content');
            if (content) {
                content.select();
                content.setSelectionRange(0, 99999); // For mobile devices
                navigator.clipboard.writeText(content.value).then(function() {
                    // Temporarily change button text to show success
                    const btn = event.target;
                    const originalText = btn.textContent;
                    btn.textContent = '✅ Copied!';
                    btn.style.backgroundColor = '#4CAF50';
                    setTimeout(() => {
                        btn.textContent = originalText;
                        btn.style.backgroundColor = '#2196F3';
                    }, 2000);
                }).catch(function(err) {
                    console.error('Could not copy text: ', err);
                    alert('Failed to copy content to clipboard');
                });
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('url').value = '';
            document.getElementById('url').focus();
        }

        // Auto-focus on URL input when page loads
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('url').focus();
        });

        // Handle Enter key in URL input
        document.getElementById('url').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                document.querySelector('form').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
