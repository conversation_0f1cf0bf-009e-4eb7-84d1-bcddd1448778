<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<body>
    <div id="textLines" th:fragment="textLinesList">
        <div class="header-container">
            <h2>Stored Text Lines - do not store sensitive information!</h2>
            <div class="button-group">
                <button class="refresh-btn"
                        hx-post="/refresh"
                        hx-target="#textLines"
                        hx-swap="outerHTML"
                        title="Refresh UI list">
                    Refresh UI
                </button>
            </div>
        </div>
        <div th:if="${textLines.empty}">
            <p>No text lines added yet.</p>
        </div>
        <div th:each="line : ${textLines}" class="text-line">
            <!-- Display separator with title and delete button -->
            <div th:if="${line.separator}" class="text-line-container">
                <div class="separator-container">
                    <div class="separator-line"></div>
                    <span class="separator-title" th:text="${line.content}"></span>
                    <div class="separator-line"></div>
                </div>
                <div class="text-actions">
                    <form hx-post="/move-up"
                          hx-target="#textLines"
                          hx-swap="outerHTML"
                          class="move-form">
                        <input type="hidden" name="id" th:value="${line.id}">
                        <button type="submit" class="move-btn move-up-btn">↑</button>
                    </form>
                    <form hx-post="/move-down"
                          hx-target="#textLines"
                          hx-swap="outerHTML"
                          class="move-form">
                        <input type="hidden" name="id" th:value="${line.id}">
                        <button type="submit" class="move-btn move-down-btn">↓</button>
                    </form>
                    <form hx-post="/delete"
                          hx-target="#textLines"
                          hx-swap="outerHTML"
                          class="delete-form"
                          hx-confirm="Are you sure you want to delete this separator?">
                        <input type="hidden" name="id" th:value="${line.id}">
                        <button type="submit" class="delete-btn">Delete</button>
                    </form>
                </div>
            </div>

            <!-- Display regular text line with copy and delete buttons -->
            <div th:unless="${line.separator}" class="text-line-container">
                <div class="text-content">
                    <p th:text="${line.content}"></p>
                </div>
                <div class="text-actions">
                    <button class="copy-btn"
                            th:attr="data-content=${line.content}"
                            onclick="copyToClipboard(this.getAttribute('data-content'), event)">
                        Copy
                    </button>
                    <form hx-post="/move-up"
                          hx-target="#textLines"
                          hx-swap="outerHTML"
                          class="move-form">
                        <input type="hidden" name="id" th:value="${line.id}">
                        <button type="submit" class="move-btn move-up-btn">↑</button>
                    </form>
                    <form hx-post="/move-down"
                          hx-target="#textLines"
                          hx-swap="outerHTML"
                          class="move-form">
                        <input type="hidden" name="id" th:value="${line.id}">
                        <button type="submit" class="move-btn move-down-btn">↓</button>
                    </form>
                    <form hx-post="/delete"
                          hx-target="#textLines"
                          hx-swap="outerHTML"
                          class="delete-form"
                          hx-confirm="Are you sure you want to delete this text line?">
                        <input type="hidden" name="id" th:value="${line.id}">
                        <button type="submit" class="delete-btn">Delete</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html>