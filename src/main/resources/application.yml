server:
  port: 8080

management:
  # Run management endpoints on separate port for security
  server:
    port: 8081
    address: 127.0.0.1  # Only allow local access to management port
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
  health:
    defaults:
      enabled: true
  # Security for management endpoints
  security:
    enabled: false  # Disable security for now, can be enabled later

spring:
  application:
    name: transactionsstore
