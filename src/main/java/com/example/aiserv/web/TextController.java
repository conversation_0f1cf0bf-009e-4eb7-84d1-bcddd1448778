package com.example.aiserv.web;

import com.example.aiserv.ai.AIRequestBody;
import com.example.aiserv.model.AIRequest;
import com.example.aiserv.services.AIService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;

@RestController
public class TextController
{
    private static final Logger logger = LoggerFactory.getLogger(TextController.class);
    private final AIService aiService;

    @Autowired
    public TextController(final AIService aiService)
    {
        this.aiService = aiService;
    }

    @PostMapping("/text")
    public ResponseEntity<String> postText(@RequestParam(name = "text") final String text,
                                           @RequestParam(name = "system", required = false) final String system)
    {
        if (text == null || text.trim().isEmpty())
        {
            return ResponseEntity.badRequest().body("Text parameter is required");
        }

        try
        {
            final var request = new AIRequest("text", text, system, Instant.now());
            aiService.processRequest(request);
            return ResponseEntity.ok("Request processed successfully");
        }
        catch (final IllegalArgumentException e)
        {
            logger.error("Invalid request: {}", e.getMessage());
            return ResponseEntity.badRequest().body("Invalid request: " + e.getMessage());
        }
        catch (final Exception e)
        {
            logger.error("Error processing request", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing request");
        }
    }

    @PostMapping("/sendPrompt")
    public ResponseEntity<String> postSyncText(@RequestBody AIRequestBody request)
    {
        if (request == null || request.getPrompt().trim().isEmpty())
        {
            return ResponseEntity.badRequest().body("Text parameter is required");
        }

        try
        {
            if (null == request.getImage())
            {
                return ResponseEntity.ok(aiService.processSyncRequest(request.getPrompt()));
            }
            else
            {
                return ResponseEntity.ok(aiService.processSyncRequest(request));
            }
        }
        catch (final IllegalArgumentException e)
        {
            logger.error("Invalid prompt: {}", e.getMessage());
            return ResponseEntity.badRequest().body("Invalid request: " + e.getMessage());
        }
        catch (final Exception e)
        {
            logger.error("Error processing request", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing request");
        }
    }
//    @PostMapping("/sendPrompt")
//    public ResponseEntity<String> postSyncText(@RequestParam(name = "prompt") final String prompt,
//                                               @RequestParam(name = "model", required = false) final String model,
//                                               @RequestParam(name = "system", required = false) final String system)
//    {
//        if (prompt == null || prompt.trim().isEmpty())
//        {
//            return ResponseEntity.badRequest().body("Text parameter is required");
//        }
//
//        try
//        {
//            return ResponseEntity.ok(aiService.processSyncRequest(prompt));
//        }
//        catch (final IllegalArgumentException e)
//        {
//            logger.error("Invalid prompt: {}", e.getMessage());
//            return ResponseEntity.badRequest().body("Invalid request: " + e.getMessage());
//        }
//        catch (final Exception e)
//        {
//            logger.error("Error processing request", e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing request");
//        }
//    }
}