package com.example.aiserv.persist;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public class MapResultRepository<K, V> implements ResultRepository<K, V> {
    private final Map<K, V> resultMap;

    public MapResultRepository() {
        this.resultMap = new ConcurrentHashMap<>();
    }

    @Override
    public void save(final K id, final V result) {
        Objects.requireNonNull(id, "id must not be null");
        Objects.requireNonNull(result, "result must not be null");
        resultMap.put(id, result);
    }

    @Override
    public Set<K> findAllKeys() {
        return resultMap.keySet();
    }

    @Override
    public Optional<V> findById(final K id) {
        return Optional.ofNullable(resultMap.get(id));
    }

    @Override
    public boolean delete(final K id) {
        return resultMap.remove(id) != null;
    }
}
