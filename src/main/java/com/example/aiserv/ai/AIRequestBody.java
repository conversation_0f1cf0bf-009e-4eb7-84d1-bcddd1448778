package com.example.aiserv.ai;

/**
 * Transfer Object for data between UI and AI request service wrapper
 */
public class AIRequestBody
{
    // User prompt
    private final String prompt;

    // optional image data
    private final String image;

    public AIRequestBody(final String prompt, final String image)
    {
        this.prompt = prompt;
        this.image = image;
    }

    public String getPrompt()
    {
        return prompt;
    }

    public String getImage()
    {
        return image;
    }
}
