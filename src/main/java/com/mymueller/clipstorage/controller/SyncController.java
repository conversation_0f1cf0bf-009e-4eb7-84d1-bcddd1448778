package com.mymueller.clipstorage.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;

@RestController
public class SyncController {
    // Counter that increments whenever text lines are updated
    private final AtomicLong updateCounter = new AtomicLong(Math.abs(new Random().nextInt()));
    
    /**
     * Increments the update counter
     */
    public void updateCounter() {
        updateCounter.incrementAndGet();
    }
    
    /**
     * Returns the current update counter value
     * Clients can poll this endpoint to check if there are updates
     */
    @GetMapping("/api/sync/counter")
    public ResponseEntity<Long> getUpdateCounter() {
        return ResponseEntity.ok(updateCounter.get());
    }
}
