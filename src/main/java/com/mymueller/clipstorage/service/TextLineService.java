package com.mymueller.clipstorage.service;

import com.mymueller.clipstorage.model.TextLine;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class TextLineService {
    private List<TextLine> textLines = new ArrayList<>();
    private final StorageService storageService;
    private final SyncController syncController;

    public TextLineService(final StorageService storageService, final SyncController syncController)
    {
        this.storageService = storageService;
        this.syncController = syncController;
    }

    @PostConstruct
    public void init()
    {
        // Load text lines from storage on startup
        final List<TextLine> loadedLines = storageService.loadTextLines();
        if (loadedLines != null && !loadedLines.isEmpty())
        {
            textLines = loadedLines;
        }
    }

    public boolean addTextLine(final String content)
    {
        // Existing validation logic
        if (content == null || content.trim().isEmpty()) {
            return false;
        }

        final boolean isDuplicate = textLines.stream()
                                             .anyMatch(line -> line.getContent().equals(content));

        if (!isDuplicate) {
            textLines.add(new TextLine(content));
            // Save changes to storage
            storageService.saveTextLines(textLines);
            // Increment the update counter to notify clients of changes
            syncController.incrementUpdateCounter();
            return true;
        }

        return false;
    }

    public boolean addSeparator(final String title)
    {
        // Existing validation logic
        if (textLines.isEmpty())
        {
            return false;
        }

        if (textLines.getLast().isSeparator())
        {
            return false;
        }

        textLines.add(new TextLine(true, title));
        // Save changes to storage
        storageService.saveTextLines(textLines);
        // Increment the update counter to notify clients of changes
        syncController.incrementUpdateCounter();
        return true;
    }

    public List<TextLine> getAllTextLines() {
        return Collections.unmodifiableList(textLines);
    }

    public void deleteTextLine(final String id)
    {
        final boolean removed = textLines.removeIf(line -> line.getId().equals(id));
        if (removed)
        {
            // Save changes to storage
            storageService.saveTextLines(textLines);
            // Increment the update counter to notify clients of changes
            syncController.incrementUpdateCounter();
        }
    }

    /**
     * Reorders a text line by moving it from one position to another.
     *
     * @param sourceId The ID of the text line to move
     * @param targetId The ID of the text line to move it before/after
     * @param position Either "before" or "after" to indicate where to place the source relative to the target
     * @return true if the reordering was successful, false otherwise
     */
    public boolean reorder(final String sourceId, final String targetId, final String position) {
        // Don't do anything if source and target are the same
        if (sourceId.equals(targetId)) {
            return false;
        }

        final int sourceIndex = findIndexById(sourceId);
        final int targetIndex = findIndexById(targetId);

        // Check if both source and target exist
        if (sourceIndex == -1 || targetIndex == -1) {
            return false;
        }

        // Remove the source item
        final TextLine sourceItem = textLines.remove(sourceIndex);

        // Calculate the new index based on the position and whether the source was before the target
        int newIndex = targetIndex;
        if (sourceIndex < targetIndex) {
            // If source was before target, the target index is now one less
            newIndex--;
        }

        // Adjust the index based on the position
        if ("after".equals(position)) {
            newIndex++;
        }

        // Insert the source item at the new index
        textLines.add(newIndex, sourceItem);

        // Save changes to storage
        storageService.saveTextLines(textLines);
        // Increment the update counter to notify clients of changes
        syncController.incrementUpdateCounter();
        return true;
    }

    private int findIndexById(final String id)
    {
        for (int i = 0; i < textLines.size(); i++)
        {
            if (textLines.get(i).getId().equals(id))
            {
                return i;
            }
        }
        return -1;
    }
}