package com.mymueller.transactionsstore.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PastOrPresent;
import jakarta.validation.constraints.Size;

import java.time.LocalDate;
import java.util.Objects;

/**
 * JPA Entity representing transaction data in the database.
 * This entity maps to the 'transaction_data' table and contains all transaction information.
 */
@Entity
@Table(name = "transaction_data")
public class TransactionDataEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "Transaction date is required")
    @PastOrPresent(message = "Transaction date cannot be in the future")
    @Column(name = "transaction_date", nullable = false)
    private LocalDate date;

    @NotBlank(message = "Account name is required")
    @Size(max = 150, message = "Account name cannot exceed 150 characters")
    @Column(name = "account_name", nullable = false)
    private String accountname;

    @NotNull(message = "Amount is required")
    @Column(nullable = false)
    private Long amount;

    @NotBlank(message = "Participant is required")
    @Size(max = 150, message = "Participant cannot exceed 150 characters")
    @Column(nullable = false)
    private String participant;

    @NotBlank(message = "Description is required")
    @Size(max = 200, message = "Description cannot exceed 200 characters")
    @Column(nullable = false)
    private String description;

    @Column(name = "reference_id")
    private Long referenceid;

    @Size(max = 200, message = "Tax relevant information cannot exceed 200 characters")
    @Column(name = "tax_relevant")
    private String taxRelevant;

    @Size(max = 200, message = "Extra costs information cannot exceed 200 characters")
    @Column(name = "extra_costs")
    private String extraCosts;

    @Size(max = 50, message = "ASN cannot exceed 50 characters")
    @Column(name = "asn")
    private String asn;

    // Constructors
    public TransactionDataEntity() {
    }

    public TransactionDataEntity(LocalDate date, String accountname, Long amount, String participant, String description) {
        this.date = date;
        this.accountname = accountname;
        this.amount = amount;
        this.participant = participant;
        this.description = description;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getAccountname() {
        return accountname;
    }

    public void setAccountname(String accountname) {
        this.accountname = accountname;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getParticipant() {
        return participant;
    }

    public void setParticipant(String participant) {
        this.participant = participant;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getReferenceid() {
        return referenceid;
    }

    public void setReferenceid(Long referenceid) {
        this.referenceid = referenceid;
    }

    public String getTaxRelevant() {
        return taxRelevant;
    }

    public void setTaxRelevant(String taxRelevant) {
        this.taxRelevant = taxRelevant;
    }

    public String getExtraCosts() {
        return extraCosts;
    }

    public void setExtraCosts(String extraCosts) {
        this.extraCosts = extraCosts;
    }

    public String getAsn() {
        return asn;
    }

    public void setAsn(String asn) {
        this.asn = asn;
    }

    // equals and hashCode
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TransactionDataEntity that = (TransactionDataEntity) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    // toString
    @Override
    public String toString() {
        return "TransactionDataEntity{" +
                "id=" + id +
                ", date=" + date +
                ", accountname='" + accountname + '\'' +
                ", amount=" + amount +
                ", participant='" + participant + '\'' +
                ", description='" + description + '\'' +
                ", referenceid=" + referenceid +
                ", taxRelevant='" + taxRelevant + '\'' +
                ", extraCosts='" + extraCosts + '\'' +
                ", asn='" + asn + '\'' +
                '}';
    }
}
