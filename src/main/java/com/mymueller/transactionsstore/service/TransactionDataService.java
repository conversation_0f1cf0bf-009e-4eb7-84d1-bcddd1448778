package com.mymueller.transactionsstore.service;

import com.mymueller.transactionsstore.entity.TransactionDataEntity;
import com.mymueller.transactionsstore.model.CreateTransactionDataRequest;
import com.mymueller.transactionsstore.model.TransactionData;
import com.mymueller.transactionsstore.model.UpdateTransactionDataRequest;
import com.mymueller.transactionsstore.repository.TransactionDataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service layer for TransactionData operations.
 * Handles business logic and coordinates between controllers and repositories.
 */
@Service
@Transactional
public class TransactionDataService {

    private final TransactionDataRepository repository;

    @Autowired
    public TransactionDataService(TransactionDataRepository repository) {
        this.repository = repository;
    }

    /**
     * Create a new transaction.
     *
     * @param request the transaction creation request
     * @return the created transaction data
     */
    public TransactionData createTransaction(CreateTransactionDataRequest request) {
        TransactionDataEntity entity = new TransactionDataEntity();
        entity.setDate(request.getDate());
        entity.setAccountname(request.getAccountname());
        entity.setAmount(request.getAmount());
        entity.setParticipant(request.getParticipant());
        entity.setDescription(request.getDescription());
        entity.setReferenceid(request.getReferenceid());
        entity.setTaxRelevant(request.getTaxRelevant());
        entity.setExtraCosts(request.getExtraCosts());
        entity.setAsn(request.getAsn());

        TransactionDataEntity savedEntity = repository.save(entity);
        return convertToModel(savedEntity);
    }

    /**
     * Get all transactions with optional filters.
     *
     * @param startDate start date filter
     * @param endDate end date filter
     * @param taxRelevant tax relevant filter
     * @param extraCosts extra costs filter
     * @param accountname account name filter
     * @param participant participant filter
     * @param referenceid reference ID filter
     * @return list of filtered transactions
     */
    @Transactional(readOnly = true)
    public List<TransactionData> getAllTransactions(LocalDate startDate, LocalDate endDate, 
                                                   String taxRelevant, String extraCosts, 
                                                   String accountname, String participant, 
                                                   Long referenceid) {
        List<TransactionDataEntity> entities;
        
        // Use the complex query if any filters are provided
        if (startDate != null || endDate != null || taxRelevant != null || extraCosts != null ||
            accountname != null || participant != null || referenceid != null) {
            entities = repository.findTransactionsWithFilters(
                startDate, endDate, accountname, participant, referenceid, taxRelevant, extraCosts);
        } else {
            entities = repository.findAll();
        }

        return entities.stream()
                .map(this::convertToModel)
                .collect(Collectors.toList());
    }

    /**
     * Get a transaction by ID.
     *
     * @param id the transaction ID
     * @return the transaction data if found
     */
    @Transactional(readOnly = true)
    public Optional<TransactionData> getTransactionById(Long id) {
        return repository.findById(id)
                .map(this::convertToModel);
    }

    /**
     * Update an existing transaction.
     *
     * @param id the transaction ID
     * @param request the update request
     * @return the updated transaction data if found
     */
    public Optional<TransactionData> updateTransaction(Long id, UpdateTransactionDataRequest request) {
        return repository.findById(id)
                .map(entity -> {
                    if (request.getDate() != null) {
                        entity.setDate(request.getDate());
                    }
                    if (request.getAccountname() != null) {
                        entity.setAccountname(request.getAccountname());
                    }
                    if (request.getAmount() != null) {
                        entity.setAmount(request.getAmount());
                    }
                    if (request.getParticipant() != null) {
                        entity.setParticipant(request.getParticipant());
                    }
                    if (request.getDescription() != null) {
                        entity.setDescription(request.getDescription());
                    }
                    if (request.getReferenceid() != null) {
                        entity.setReferenceid(request.getReferenceid());
                    }
                    if (request.getTaxRelevant() != null) {
                        entity.setTaxRelevant(request.getTaxRelevant());
                    }
                    if (request.getExtraCosts() != null) {
                        entity.setExtraCosts(request.getExtraCosts());
                    }
                    if (request.getAsn() != null) {
                        entity.setAsn(request.getAsn());
                    }

                    TransactionDataEntity savedEntity = repository.save(entity);
                    return convertToModel(savedEntity);
                });
    }

    /**
     * Delete a transaction by ID.
     *
     * @param id the transaction ID
     * @return true if the transaction was deleted, false if not found
     */
    public boolean deleteTransaction(Long id) {
        if (repository.existsById(id)) {
            repository.deleteById(id);
            return true;
        }
        return false;
    }

    /**
     * Get all distinct account names.
     *
     * @return list of distinct account names
     */
    @Transactional(readOnly = true)
    public List<String> getDistinctAccountnames() {
        return repository.findDistinctAccountnames();
    }

    /**
     * Get all distinct participants.
     *
     * @return list of distinct participants
     */
    @Transactional(readOnly = true)
    public List<String> getDistinctParticipants() {
        return repository.findDistinctParticipants();
    }

    /**
     * Get all distinct tax relevant classifications.
     *
     * @return list of distinct tax relevant classifications
     */
    @Transactional(readOnly = true)
    public List<String> getDistinctTaxRelevant() {
        return repository.findDistinctTaxRelevant();
    }

    /**
     * Get all distinct extra costs classifications.
     *
     * @return list of distinct extra costs classifications
     */
    @Transactional(readOnly = true)
    public List<String> getDistinctExtraCosts() {
        return repository.findDistinctExtraCosts();
    }

    /**
     * Clear all transactions (for testing purposes).
     */
    public void clearAllTransactions() {
        repository.deleteAll();
    }

    /**
     * Convert entity to model.
     *
     * @param entity the entity to convert
     * @return the converted model
     */
    private TransactionData convertToModel(TransactionDataEntity entity) {
        TransactionData model = new TransactionData();
        model.setId(entity.getId());
        model.setDate(entity.getDate());
        model.setAccountname(entity.getAccountname());
        model.setAmount(entity.getAmount());
        model.setParticipant(entity.getParticipant());
        model.setDescription(entity.getDescription());
        model.setReferenceid(entity.getReferenceid());
        model.setTaxRelevant(entity.getTaxRelevant());
        model.setExtraCosts(entity.getExtraCosts());
        model.setAsn(entity.getAsn());
        return model;
    }
}
