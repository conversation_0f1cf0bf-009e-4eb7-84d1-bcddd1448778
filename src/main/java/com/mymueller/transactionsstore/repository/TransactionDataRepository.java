package com.mymueller.transactionsstore.repository;

import com.mymueller.transactionsstore.entity.TransactionDataEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * Repository interface for TransactionDataEntity.
 * Provides database access methods for transaction data operations.
 */
@Repository
public interface TransactionDataRepository extends JpaRepository<TransactionDataEntity, Long> {

    /**
     * Find transactions by date range.
     *
     * @param startDate the start date (inclusive)
     * @param endDate the end date (inclusive)
     * @return list of transactions within the date range
     */
    List<TransactionDataEntity> findByDateBetween(LocalDate startDate, LocalDate endDate);

    /**
     * Find transactions by account name.
     *
     * @param accountname the account name
     * @return list of transactions for the account
     */
    List<TransactionDataEntity> findByAccountname(String accountname);

    /**
     * Find transactions by participant.
     *
     * @param participant the participant name
     * @return list of transactions for the participant
     */
    List<TransactionDataEntity> findByParticipant(String participant);

    /**
     * Find transactions by reference ID.
     *
     * @param referenceid the reference ID
     * @return list of transactions with the reference ID
     */
    List<TransactionDataEntity> findByReferenceid(Long referenceid);

    /**
     * Find transactions containing specific tax relevant information.
     *
     * @param taxRelevant the tax relevant text to search for
     * @return list of transactions containing the tax relevant information
     */
    List<TransactionDataEntity> findByTaxRelevantContainingIgnoreCase(String taxRelevant);

    /**
     * Find transactions containing specific extra costs information.
     *
     * @param extraCosts the extra costs text to search for
     * @return list of transactions containing the extra costs information
     */
    List<TransactionDataEntity> findByExtraCostsContainingIgnoreCase(String extraCosts);

    /**
     * Find all distinct account names.
     *
     * @return list of distinct account names
     */
    @Query("SELECT DISTINCT t.accountname FROM TransactionDataEntity t WHERE t.accountname IS NOT NULL ORDER BY t.accountname")
    List<String> findDistinctAccountnames();

    /**
     * Find all distinct participants.
     *
     * @return list of distinct participants
     */
    @Query("SELECT DISTINCT t.participant FROM TransactionDataEntity t WHERE t.participant IS NOT NULL ORDER BY t.participant")
    List<String> findDistinctParticipants();

    /**
     * Find all distinct tax relevant classifications.
     *
     * @return list of distinct tax relevant classifications
     */
    @Query("SELECT DISTINCT t.taxRelevant FROM TransactionDataEntity t WHERE t.taxRelevant IS NOT NULL AND t.taxRelevant != '' ORDER BY t.taxRelevant")
    List<String> findDistinctTaxRelevant();

    /**
     * Find all distinct extra costs classifications.
     *
     * @return list of distinct extra costs classifications
     */
    @Query("SELECT DISTINCT t.extraCosts FROM TransactionDataEntity t WHERE t.extraCosts IS NOT NULL AND t.extraCosts != '' ORDER BY t.extraCosts")
    List<String> findDistinctExtraCosts();

    /**
     * Complex query to find transactions with multiple filters.
     *
     * @param startDate start date filter (optional)
     * @param endDate end date filter (optional)
     * @param accountname account name filter (optional)
     * @param participant participant filter (optional)
     * @param referenceid reference ID filter (optional)
     * @param taxRelevant tax relevant filter (optional)
     * @param extraCosts extra costs filter (optional)
     * @return list of filtered transactions
     */
    @Query("SELECT t FROM TransactionDataEntity t WHERE " +
           "(:startDate IS NULL OR t.date >= :startDate) AND " +
           "(:endDate IS NULL OR t.date <= :endDate) AND " +
           "(:accountname IS NULL OR t.accountname = :accountname) AND " +
           "(:participant IS NULL OR t.participant = :participant) AND " +
           "(:referenceid IS NULL OR t.referenceid = :referenceid) AND " +
           "(:taxRelevant IS NULL OR LOWER(t.taxRelevant) LIKE LOWER(CONCAT('%', :taxRelevant, '%'))) AND " +
           "(:extraCosts IS NULL OR LOWER(t.extraCosts) LIKE LOWER(CONCAT('%', :extraCosts, '%')))")
    List<TransactionDataEntity> findTransactionsWithFilters(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("accountname") String accountname,
            @Param("participant") String participant,
            @Param("referenceid") Long referenceid,
            @Param("taxRelevant") String taxRelevant,
            @Param("extraCosts") String extraCosts
    );
}
