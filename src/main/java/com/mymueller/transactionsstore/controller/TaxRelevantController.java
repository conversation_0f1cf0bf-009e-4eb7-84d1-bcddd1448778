package com.mymueller.transactionsstore.controller;

import com.mymueller.transactionsstore.api.TaxrelevantApi;
import com.mymueller.transactionsstore.model.TaxRelevant;
import com.mymueller.transactionsstore.model.TransactionData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of the generated TaxRelevantApi interface
 * This controller provides distinct tax relevant classifications from existing transactions
 */
@RestController
public class TaxRelevantController implements TaxrelevantApi {

    private final TransactionsController transactionsController;

    @Autowired
    public TaxRelevantController(TransactionsController transactionsController) {
        this.transactionsController = transactionsController;
    }

    @Override
    public ResponseEntity<List<TaxRelevant>> getTaxRelevant() {
        // Get all transactions from the transactions controller
        ResponseEntity<List<TransactionData>> transactionsResponse = 
            transactionsController.getAllTransactions(null, null, null, null, null, null, null);
        
        if (transactionsResponse.getBody() == null) {
            return new ResponseEntity<>(List.of(), HttpStatus.OK);
        }

        // Extract distinct tax relevant classifications
        List<TaxRelevant> taxRelevantList = transactionsResponse.getBody().stream()
            .map(TransactionData::getTaxRelevant)
            .filter(taxRelevant -> taxRelevant != null && !taxRelevant.trim().isEmpty())
            .distinct()
            .sorted()
            .map(classification -> new TaxRelevant(classification))
            .collect(Collectors.toList());

        return new ResponseEntity<>(taxRelevantList, HttpStatus.OK);
    }
}
