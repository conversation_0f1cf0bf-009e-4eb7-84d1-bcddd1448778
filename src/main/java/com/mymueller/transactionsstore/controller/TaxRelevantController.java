package com.mymueller.transactionsstore.controller;

import com.mymueller.transactionsstore.api.TaxrelevantApi;
import com.mymueller.transactionsstore.model.TaxRelevant;
import com.mymueller.transactionsstore.service.TransactionDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of the generated TaxRelevantApi interface
 * This controller provides distinct tax relevant classifications from existing transactions
 */
@RestController
public class TaxRelevantController implements TaxrelevantApi {

    private final TransactionDataService transactionDataService;

    @Autowired
    public TaxRelevantController(TransactionDataService transactionDataService) {
        this.transactionDataService = transactionDataService;
    }

    @Override
    public ResponseEntity<List<TaxRelevant>> getTaxRelevant() {
        // Get distinct tax relevant classifications from the service
        List<String> distinctTaxRelevant = transactionDataService.getDistinctTaxRelevant();

        // Convert to TaxRelevant model objects
        List<TaxRelevant> taxRelevantList = distinctTaxRelevant.stream()
            .map(classification -> new TaxRelevant(classification))
            .collect(Collectors.toList());

        return new ResponseEntity<>(taxRelevantList, HttpStatus.OK);
    }
}
