package com.mymueller.transactionsstore.controller;

import com.mymueller.transactionsstore.api.AccountsApi;
import com.mymueller.transactionsstore.model.Account;
import com.mymueller.transactionsstore.service.TransactionDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of the generated AccountsApi interface
 * This is a simple in-memory implementation for demonstration purposes
 */
@RestController()
public class AccountsController implements AccountsApi {

    // Simple in-memory storage for demonstration
//    private final ConcurrentHashMap<String, Account> accounts = new ConcurrentHashMap<>();
//    private final AtomicLong idCounter = new AtomicLong(1);

    @Override
    public ResponseEntity<List<Account>> getAccounts() {
        return new ResponseEntity<>(Collections.emptyList(), HttpStatus.OK);
    }

}
