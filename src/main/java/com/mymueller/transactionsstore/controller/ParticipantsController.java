package com.mymueller.transactionsstore.controller;

import com.mymueller.transactionsstore.api.ParticipantsApi;
import com.mymueller.transactionsstore.model.Participants;
import com.mymueller.transactionsstore.service.TransactionDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of the generated ParticipantsApi interface
 * This implementation uses the database-backed service layer to retrieve distinct participant names from transactions
 */
@RestController
public class ParticipantsController implements ParticipantsApi {

    private final TransactionDataService transactionDataService;

    @Autowired
    public ParticipantsController(TransactionDataService transactionDataService) {
        this.transactionDataService = transactionDataService;
    }

    @Override
    public ResponseEntity<List<Participants>> getParticipants() {
        // Get distinct participant names from the service
        List<String> distinctParticipantNames = transactionDataService.getDistinctParticipants();

        // Convert to Participants model objects
        List<Participants> participants = distinctParticipantNames.stream()
            .map(participantName -> new Participants(participantName))
            .collect(Collectors.toList());

        return new ResponseEntity<>(participants, HttpStatus.OK);
    }
}
