package com.mymueller.transactionsstore.controller;

import com.mymueller.transactionsstore.api.TransactionsApi;
import com.mymueller.transactionsstore.model.CreateTransactionDataRequest;
import com.mymueller.transactionsstore.model.TransactionData;
import com.mymueller.transactionsstore.model.UpdateTransactionDataRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * Implementation of the generated TransactionsApi interface
 * This is a simple in-memory implementation for demonstration purposes
 */
@RestController
public class TransactionsController implements TransactionsApi {

    // Simple in-memory storage for demonstration
    private final ConcurrentHashMap<Long, TransactionData> transactions = new ConcurrentHashMap<>();
    private final AtomicLong idCounter = new AtomicLong(1);

    @Override
    public ResponseEntity<TransactionData> createTransaction(final CreateTransactionDataRequest createTransactionDataRequest) {
        // Validate input
        if (createTransactionDataRequest.getDescription() != null && 
            createTransactionDataRequest.getDescription().length() > 200) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }

        // Create new transaction
        final Long transactionId = idCounter.getAndIncrement();
        final TransactionData transaction = new TransactionData();
        transaction.setId(transactionId);
        transaction.setDate(createTransactionDataRequest.getDate());
        transaction.setAccountname(createTransactionDataRequest.getAccountname());
        transaction.setAmount(createTransactionDataRequest.getAmount());
        transaction.setParticipant(createTransactionDataRequest.getParticipant());
        transaction.setDescription(createTransactionDataRequest.getDescription());
        transaction.setReferenceid(createTransactionDataRequest.getReferenceid());
        transaction.setTaxRelevant(createTransactionDataRequest.getTaxRelevant());
        transaction.setExtraCosts(createTransactionDataRequest.getExtraCosts());
        transaction.setAsn(createTransactionDataRequest.getAsn());

        // Store the transaction
        transactions.put(transactionId, transaction);

        return new ResponseEntity<>(transaction, HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<Void> deleteTransaction(final Long transactionId) {
        final TransactionData removed = transactions.remove(transactionId);
        
        if (removed == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Override
    public ResponseEntity<List<TransactionData>> getAllTransactions(
            final LocalDate startDate,
            final LocalDate endDate,
            final String taxRelevant,
            final String extraCosts,
            final String accountname,
            final String participant,
            final Long referenceid) {
        
        List<TransactionData> result = new ArrayList<>(transactions.values());

        // Apply filters
        if (startDate != null) {
            result = result.stream()
                .filter(t -> t.getDate() != null && !t.getDate().isBefore(startDate))
                .collect(Collectors.toList());
        }

        if (endDate != null) {
            result = result.stream()
                .filter(t -> t.getDate() != null && !t.getDate().isAfter(endDate))
                .collect(Collectors.toList());
        }

        if (taxRelevant != null && !taxRelevant.trim().isEmpty()) {
            result = result.stream()
                .filter(t -> t.getTaxRelevant() != null &&
                    t.getTaxRelevant().toLowerCase().contains(taxRelevant.toLowerCase()))
                .collect(Collectors.toList());
        }

        if (extraCosts != null && !extraCosts.trim().isEmpty()) {
            result = result.stream()
                .filter(t -> t.getExtraCosts() != null &&
                    t.getExtraCosts().toLowerCase().contains(extraCosts.toLowerCase()))
                .collect(Collectors.toList());
        }

        if (accountname != null && !accountname.trim().isEmpty()) {
            result = result.stream()
                .filter(t -> t.getAccountname() != null && 
                    t.getAccountname().toLowerCase().contains(accountname.toLowerCase()))
                .collect(Collectors.toList());
        }

        if (participant != null && !participant.trim().isEmpty()) {
            result = result.stream()
                .filter(t -> t.getParticipant() != null && 
                    t.getParticipant().toLowerCase().contains(participant.toLowerCase()))
                .collect(Collectors.toList());
        }

        if (referenceid != null) {
            result = result.stream()
                .filter(t -> referenceid.equals(t.getReferenceid()))
                .collect(Collectors.toList());
        }

        return ResponseEntity.ok(result);
    }

    @Override
    public ResponseEntity<TransactionData> getTransactionById(final Long transactionId) {
        final TransactionData transaction = transactions.get(transactionId);
        
        if (transaction == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        return ResponseEntity.ok(transaction);
    }

    @Override
    public ResponseEntity<TransactionData> updateTransaction(final Long transactionId, final UpdateTransactionDataRequest updateTransactionDataRequest) {
        final TransactionData existingTransaction = transactions.get(transactionId);
        
        if (existingTransaction == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        // Validate input
        if (updateTransactionDataRequest.getDescription() != null && 
            updateTransactionDataRequest.getDescription().length() > 200) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }

        // Update the transaction
        final TransactionData updatedTransaction = new TransactionData();
        updatedTransaction.setId(existingTransaction.getId());
        updatedTransaction.setDate(updateTransactionDataRequest.getDate() != null ? 
            updateTransactionDataRequest.getDate() : existingTransaction.getDate());
        updatedTransaction.setAccountname(updateTransactionDataRequest.getAccountname() != null ? 
            updateTransactionDataRequest.getAccountname() : existingTransaction.getAccountname());
        updatedTransaction.setAmount(updateTransactionDataRequest.getAmount() != null ? 
            updateTransactionDataRequest.getAmount() : existingTransaction.getAmount());
        updatedTransaction.setParticipant(updateTransactionDataRequest.getParticipant() != null ? 
            updateTransactionDataRequest.getParticipant() : existingTransaction.getParticipant());
        updatedTransaction.setDescription(updateTransactionDataRequest.getDescription() != null ? 
            updateTransactionDataRequest.getDescription() : existingTransaction.getDescription());
        updatedTransaction.setReferenceid(updateTransactionDataRequest.getReferenceid() != null ? 
            updateTransactionDataRequest.getReferenceid() : existingTransaction.getReferenceid());
        updatedTransaction.setTaxRelevant(updateTransactionDataRequest.getTaxRelevant() != null ?
            updateTransactionDataRequest.getTaxRelevant() : existingTransaction.getTaxRelevant());
        updatedTransaction.setExtraCosts(updateTransactionDataRequest.getExtraCosts() != null ?
            updateTransactionDataRequest.getExtraCosts() : existingTransaction.getExtraCosts());
        updatedTransaction.setAsn(updateTransactionDataRequest.getAsn() != null ?
            updateTransactionDataRequest.getAsn() : existingTransaction.getAsn());

        // Store the updated transaction
        transactions.put(transactionId, updatedTransaction);

        return ResponseEntity.ok(updatedTransaction);
    }

    // Helper methods for testing - not part of the API
    public void clearTransactions() {
        transactions.clear();
        idCounter.set(1);
    }

    public int getTransactionCount() {
        return transactions.size();
    }
}
