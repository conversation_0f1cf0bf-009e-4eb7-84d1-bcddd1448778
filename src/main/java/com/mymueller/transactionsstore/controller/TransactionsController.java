package com.mymueller.transactionsstore.controller;

import com.mymueller.transactionsstore.api.TransactionsApi;
import com.mymueller.transactionsstore.model.CreateTransactionDataRequest;
import com.mymueller.transactionsstore.model.TransactionData;
import com.mymueller.transactionsstore.model.UpdateTransactionDataRequest;
import com.mymueller.transactionsstore.service.TransactionDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of the generated TransactionsApi interface
 * This implementation uses a database-backed service layer for persistence
 */
@RestController
public class TransactionsController implements TransactionsApi {

    private final TransactionDataService transactionDataService;

    @Autowired
    public TransactionsController(TransactionDataService transactionDataService) {
        this.transactionDataService = transactionDataService;
    }

    @Override
    public ResponseEntity<TransactionData> createTransaction(final CreateTransactionDataRequest createTransactionDataRequest) {
        try {
            TransactionData createdTransaction = transactionDataService.createTransaction(createTransactionDataRequest);
            return new ResponseEntity<>(createdTransaction, HttpStatus.CREATED);
        } catch (Exception e) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public ResponseEntity<Void> deleteTransaction(final Long transactionId) {
        boolean deleted = transactionDataService.deleteTransaction(transactionId);

        if (!deleted) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Override
    public ResponseEntity<List<TransactionData>> getAllTransactions(
            final LocalDate startDate,
            final LocalDate endDate,
            final String taxRelevant,
            final String extraCosts,
            final String accountname,
            final String participant,
            final Long referenceid) {

        List<TransactionData> transactions = transactionDataService.getAllTransactions(
            startDate, endDate, taxRelevant, extraCosts, accountname, participant, referenceid);

        return ResponseEntity.ok(transactions);
    }

    @Override
    public ResponseEntity<TransactionData> getTransactionById(final Long transactionId) {
        Optional<TransactionData> transaction = transactionDataService.getTransactionById(transactionId);

        if (transaction.isEmpty()) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        return ResponseEntity.ok(transaction.get());
    }

    @Override
    public ResponseEntity<TransactionData> updateTransaction(final Long transactionId, final UpdateTransactionDataRequest updateTransactionDataRequest) {
        try {
            Optional<TransactionData> updatedTransaction = transactionDataService.updateTransaction(transactionId, updateTransactionDataRequest);

            if (updatedTransaction.isEmpty()) {
                return new ResponseEntity<>(HttpStatus.NOT_FOUND);
            }

            return ResponseEntity.ok(updatedTransaction.get());
        } catch (Exception e) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    // Helper methods for testing - not part of the API
    public void clearTransactions() {
        transactionDataService.clearAllTransactions();
    }
}
