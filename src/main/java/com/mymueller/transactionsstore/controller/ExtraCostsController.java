package com.mymueller.transactionsstore.controller;

import com.mymueller.transactionsstore.api.ExtraCostsApi;
import com.mymueller.transactionsstore.model.ExtraCosts;
import com.mymueller.transactionsstore.model.TransactionData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of the generated ExtraCostsApi interface
 * This controller provides distinct extra costs classifications from existing transactions
 */
@RestController
public class ExtraCostsController implements ExtraCostsApi {

    private final TransactionsController transactionsController;

    @Autowired
    public ExtraCostsController(TransactionsController transactionsController) {
        this.transactionsController = transactionsController;
    }

    @Override
    public ResponseEntity<List<ExtraCosts>> getExtraCosts() {
        // Get all transactions from the transactions controller
        ResponseEntity<List<TransactionData>> transactionsResponse = 
            transactionsController.getAllTransactions(null, null, null, null, null, null, null);
        
        if (transactionsResponse.getBody() == null) {
            return new ResponseEntity<>(List.of(), HttpStatus.OK);
        }

        // Extract distinct extra costs classifications
        List<ExtraCosts> extraCostsList = transactionsResponse.getBody().stream()
            .map(TransactionData::getExtraCosts)
            .filter(extraCosts -> extraCosts != null && !extraCosts.trim().isEmpty())
            .distinct()
            .sorted()
            .map(classification -> new ExtraCosts(classification))
            .collect(Collectors.toList());

        return new ResponseEntity<>(extraCostsList, HttpStatus.OK);
    }
}
