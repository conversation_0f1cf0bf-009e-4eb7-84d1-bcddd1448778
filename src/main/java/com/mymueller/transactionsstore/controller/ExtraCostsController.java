package com.mymueller.transactionsstore.controller;

import com.mymueller.transactionsstore.api.ExtracostsApi;
import com.mymueller.transactionsstore.model.ExtraCosts;
import com.mymueller.transactionsstore.service.TransactionDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of the generated ExtraCostsApi interface
 * This controller provides distinct extra costs classifications from existing transactions
 */
@RestController
public class ExtraCostsController implements ExtracostsApi {

    private final TransactionDataService transactionDataService;

    @Autowired
    public ExtraCostsController(TransactionDataService transactionDataService) {
        this.transactionDataService = transactionDataService;
    }

    @Override
    public ResponseEntity<List<ExtraCosts>> getExtraCosts() {
        // Get distinct extra costs classifications from the service
        List<String> distinctExtraCosts = transactionDataService.getDistinctExtraCosts();

        // Convert to ExtraCosts model objects
        List<ExtraCosts> extraCostsList = distinctExtraCosts.stream()
            .map(classification -> new ExtraCosts(classification))
            .collect(Collectors.toList());

        return new ResponseEntity<>(extraCostsList, HttpStatus.OK);
    }
}
