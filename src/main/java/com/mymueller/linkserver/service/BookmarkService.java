package com.mymueller.linkserver.service;

import com.mymueller.linkserver.model.Bookmark;
import com.mymueller.linkserver.model.Category;
import com.mymueller.linkserver.model.User;
import com.mymueller.linkserver.repository.BookmarkRepository;
import com.mymueller.linkserver.repository.CategoryRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service
public class BookmarkService {

    private static final Logger logger = LoggerFactory.getLogger(BookmarkService.class);

    private final BookmarkRepository bookmarkRepository;
    private final CategoryRepository categoryRepository;

    public BookmarkService(final BookmarkRepository bookmarkRepository, final CategoryRepository categoryRepository) {
        this.bookmarkRepository = bookmarkRepository;
        this.categoryRepository = categoryRepository;
    }

    /**
     * Validates and normalizes a URL
     *
     * @param url The URL to validate and normalize
     * @return The normalized URL
     * @throws IllegalArgumentException if the URL is invalid
     */
    public String validateAndNormalizeUrl(final String url) {
        if (url == null || url.trim().isEmpty()) {
            throw new IllegalArgumentException("URL cannot be empty");
        }

        // Add protocol if missing
        String normalizedUrl = url;
        if (!normalizedUrl.startsWith("http://") && !normalizedUrl.startsWith("https://")) {
            normalizedUrl = "https://" + normalizedUrl;
        }

        // Validate URL format
        try {
            new URI(normalizedUrl).toURL();
            return normalizedUrl;
        } catch (final MalformedURLException | URISyntaxException e) {
            logger.error("Invalid URL: {}", normalizedUrl, e);
            throw new IllegalArgumentException("Invalid URL format: " + e.getMessage());
        }
    }

    @Transactional
    public Bookmark createBookmark(final String url, final String title, final String description,
                                   final Bookmark.ContentType contentType, final User user, final List<String> categoryNames) {
        // Validate and normalize URL
        final String normalizedUrl = validateAndNormalizeUrl(url);

        final Bookmark bookmark = new Bookmark(normalizedUrl, title, description, contentType);
        bookmark.setUser(user);

        final Set<Category> categories = new HashSet<>();
        for (final String categoryName : categoryNames) {
            final Category category = categoryRepository.findByName(categoryName)
                                                        .orElseGet(() -> categoryRepository.save(new Category(categoryName, "")));
            categories.add(category);
        }

        bookmark.setCategories(categories);
        return bookmarkRepository.save(bookmark);
    }

    @Transactional
    public Bookmark updateBookmark(final Long id, final String url, final String title, final String description,
                                   final Bookmark.ContentType contentType, final List<String> categoryNames) {
        final Bookmark bookmark = bookmarkRepository.findById(id)
                                                    .orElseThrow(() -> new RuntimeException("Bookmark not found with id: " + id));

        // Validate and normalize URL
        final String normalizedUrl = validateAndNormalizeUrl(url);

        bookmark.setUrl(normalizedUrl);
        bookmark.setTitle(title);
        bookmark.setDescription(description);
        bookmark.setContentType(contentType);

        // Clear existing categories
        bookmark.getCategories().clear();

        // Add new categories
        for (final String categoryName : categoryNames) {
            final Category category = categoryRepository.findByName(categoryName)
                                                        .orElseGet(() -> categoryRepository.save(new Category(categoryName, "")));
            bookmark.addCategory(category);
        }

        return bookmarkRepository.save(bookmark);
    }

    @Transactional
    public void deleteBookmark(final Long id) {
        bookmarkRepository.deleteById(id);
    }

    public List<Bookmark> getUserBookmarks(final User user) {
        return bookmarkRepository.findByUserOrderByCreatedAtDesc(user);
    }

    public List<Bookmark> getUserBookmarksByCategory(final User user, final Category category) {
        return bookmarkRepository.findByUserAndCategoriesContaining(user, category);
    }

    public List<Bookmark> getUserBookmarksByContentType(final User user, final Bookmark.ContentType contentType) {
        return bookmarkRepository.findByUserAndContentType(user, contentType);
    }

    public List<Bookmark> searchUserBookmarks(final User user, final String searchTerm) {
        return bookmarkRepository.searchBookmarks(user, searchTerm);
    }

    public Optional<Bookmark> findById(final Long id) {
        return bookmarkRepository.findById(id);
    }
}
