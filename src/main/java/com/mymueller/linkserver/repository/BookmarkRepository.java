package com.mymueller.linkserver.repository;

import com.mymueller.linkserver.model.Bookmark;
import com.mymueller.linkserver.model.Category;
import com.mymueller.linkserver.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BookmarkRepository extends JpaRepository<Bookmark, Long> {
    List<Bookmark> findByUser(User user);
    
    List<Bookmark> findByUserOrderByCreatedAtDesc(User user);
    
    List<Bookmark> findByCategoriesContaining(Category category);
    
    List<Bookmark> findByUserAndCategoriesContaining(User user, Category category);
    
    List<Bookmark> findByUserAndContentType(User user, Bookmark.ContentType contentType);
    
    @Query("SELECT b FROM Bookmark b WHERE b.user = :user AND " +
           "(LOWER(b.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(b.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(b.url) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    List<Bookmark> searchBookmarks(@Param("user") User user, @Param("searchTerm") String searchTerm);
}
