package com.noobdev.plusparser;

import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;

@Component
public class GetURLContent
{
    public String getURLContent(final URL u)
    {
        try
        {
            return IOUtils.toString(u, StandardCharsets.UTF_8);
        }
        catch (final IOException e)
        {
            throw new RuntimeException(e);
        }
    }
}
