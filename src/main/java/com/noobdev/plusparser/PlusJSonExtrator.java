package com.noobdev.plusparser;

import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class PlusJSonExtrator
{
    private static final String PLUS_JSON_REGEX = ";Fusion\\.globalContent=(.+);Fusion\\.globalContentConfig";

    private final Pattern pattern;

    public PlusJSonExtrator()
    {
        pattern = Pattern.compile(PLUS_JSON_REGEX);
    }

    public Optional<String> extractJson(String plusContent)
    {
        Matcher matcher = pattern.matcher(plusContent);
        if (matcher.find()) {
            return Optional.ofNullable(matcher.group(1));
        }
        return Optional.empty();
    }
}
