package com.mymueller.transactionsstore;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mymueller.transactionsstore.controller.TransactionsController;
import com.mymueller.transactionsstore.model.CreateTransactionDataRequest;
import com.mymueller.transactionsstore.model.UpdateTransactionDataRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Tests for the TransactionsController
 */
@WebMvcTest(controllers = {TransactionsController.class})
@ActiveProfiles("test")
class TransactionsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private TransactionsController transactionsController;

    @BeforeEach
    void setUp() {
        // Clear transactions before each test
        transactionsController.clearTransactions();
    }

    @Test
    void testCreateTransaction() throws Exception {
        CreateTransactionDataRequest request = new CreateTransactionDataRequest();
        request.setDate(LocalDate.of(2024, 1, 15));
        request.setAccountname("Test Account");
        request.setAmount(125050L);
        request.setParticipant("John Doe");
        request.setDescription("Test transaction");
        request.setReferenceid(12344L);
        request.setTaxRelevant(true);
        request.setExtraCosts(false);

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.date").value("2024-01-15"))
                .andExpect(jsonPath("$.accountname").value("Test Account"))
                .andExpect(jsonPath("$.amount").value(125050))
                .andExpect(jsonPath("$.participant").value("John Doe"))
                .andExpect(jsonPath("$.description").value("Test transaction"))
                .andExpect(jsonPath("$.referenceid").value(12344))
                .andExpect(jsonPath("$.taxRelevant").value(true))
                .andExpect(jsonPath("$.extraCosts").value(false));
    }

    @Test
    void testCreateTransactionWithDefaults() throws Exception {
        CreateTransactionDataRequest request = new CreateTransactionDataRequest();
        request.setDate(LocalDate.of(2024, 1, 15));
        request.setAccountname("Test Account");
        request.setAmount(100000L);
        request.setParticipant("Jane Doe");
        request.setDescription("Simple transaction");
        // Not setting taxRelevant and extraCosts to test defaults

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.taxRelevant").value(false))
                .andExpect(jsonPath("$.extraCosts").value(false));
    }

    @Test
    void testCreateTransactionWithTooLongDescription() throws Exception {
        CreateTransactionDataRequest request = new CreateTransactionDataRequest();
        request.setDate(LocalDate.of(2024, 1, 15));
        request.setAccountname("Test Account");
        request.setAmount(100000L);
        request.setParticipant("Jane Doe");
        request.setDescription("A".repeat(201)); // 201 characters, exceeds max of 200

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testGetAllTransactions() throws Exception {
        // First create a transaction
        CreateTransactionDataRequest request = new CreateTransactionDataRequest();
        request.setDate(LocalDate.of(2024, 1, 15));
        request.setAccountname("Test Account");
        request.setAmount(100000L);
        request.setParticipant("John Doe");
        request.setDescription("Test transaction");

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated());

        // Then retrieve all transactions
        mockMvc.perform(get("/api/v1/transactions"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].accountname").value("Test Account"))
                .andExpect(jsonPath("$[0].participant").value("John Doe"));
    }

    @Test
    void testGetTransactionById() throws Exception {
        // First create a transaction
        CreateTransactionDataRequest request = new CreateTransactionDataRequest();
        request.setDate(LocalDate.of(2024, 1, 15));
        request.setAccountname("Test Account");
        request.setAmount(100000L);
        request.setParticipant("John Doe");
        request.setDescription("Test transaction");

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated());

        // Then retrieve it by ID
        mockMvc.perform(get("/api/v1/transactions/1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.accountname").value("Test Account"))
                .andExpect(jsonPath("$.participant").value("John Doe"));
    }

    @Test
    void testGetTransactionByIdNotFound() throws Exception {
        mockMvc.perform(get("/api/v1/transactions/999"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testUpdateTransaction() throws Exception {
        // First create a transaction
        CreateTransactionDataRequest createRequest = new CreateTransactionDataRequest();
        createRequest.setDate(LocalDate.of(2024, 1, 15));
        createRequest.setAccountname("Original Account");
        createRequest.setAmount(100000L);
        createRequest.setParticipant("John Doe");
        createRequest.setDescription("Original description");

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated());

        // Then update it
        UpdateTransactionDataRequest updateRequest = new UpdateTransactionDataRequest();
        updateRequest.setAccountname("Updated Account");
        updateRequest.setAmount(200000L);
        updateRequest.setDescription("Updated description");
        updateRequest.setTaxRelevant(true);

        mockMvc.perform(put("/api/v1/transactions/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.accountname").value("Updated Account"))
                .andExpect(jsonPath("$.amount").value(200000))
                .andExpect(jsonPath("$.description").value("Updated description"))
                .andExpect(jsonPath("$.taxRelevant").value(true))
                .andExpect(jsonPath("$.participant").value("John Doe")); // Should remain unchanged
    }

    @Test
    void testUpdateTransactionNotFound() throws Exception {
        UpdateTransactionDataRequest updateRequest = new UpdateTransactionDataRequest();
        updateRequest.setAccountname("Updated Account");

        mockMvc.perform(put("/api/v1/transactions/999")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isNotFound());
    }

    @Test
    void testDeleteTransaction() throws Exception {
        // First create a transaction
        CreateTransactionDataRequest request = new CreateTransactionDataRequest();
        request.setDate(LocalDate.of(2024, 1, 15));
        request.setAccountname("Test Account");
        request.setAmount(100000L);
        request.setParticipant("John Doe");
        request.setDescription("Test transaction");

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated());

        // Then delete it
        mockMvc.perform(delete("/api/v1/transactions/1"))
                .andExpect(status().isNoContent());

        // Verify it's gone
        mockMvc.perform(get("/api/v1/transactions/1"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testDeleteTransactionNotFound() throws Exception {
        mockMvc.perform(delete("/api/v1/transactions/999"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testGetTransactionsByDateRange() throws Exception {
        // Create transactions with different dates
        CreateTransactionDataRequest request1 = new CreateTransactionDataRequest();
        request1.setDate(LocalDate.of(2024, 1, 15));
        request1.setAccountname("Account 1");
        request1.setAmount(100000L);
        request1.setParticipant("John Doe");
        request1.setDescription("Transaction 1");

        CreateTransactionDataRequest request2 = new CreateTransactionDataRequest();
        request2.setDate(LocalDate.of(2024, 2, 15));
        request2.setAccountname("Account 2");
        request2.setAmount(200000L);
        request2.setParticipant("Jane Doe");
        request2.setDescription("Transaction 2");

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isCreated());

        // Filter by date range
        mockMvc.perform(get("/api/v1/transactions")
                .param("startDate", "2024-01-01")
                .param("endDate", "2024-01-31"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].accountname").value("Account 1"));
    }

    @Test
    void testGetTransactionsByFlags() throws Exception {
        // Create transactions with different flags
        CreateTransactionDataRequest request1 = new CreateTransactionDataRequest();
        request1.setDate(LocalDate.of(2024, 1, 15));
        request1.setAccountname("Account 1");
        request1.setAmount(100000L);
        request1.setParticipant("John Doe");
        request1.setDescription("Tax relevant transaction");
        request1.setTaxRelevant(true);
        request1.setExtraCosts(false);

        CreateTransactionDataRequest request2 = new CreateTransactionDataRequest();
        request2.setDate(LocalDate.of(2024, 1, 16));
        request2.setAccountname("Account 2");
        request2.setAmount(200000L);
        request2.setParticipant("Jane Doe");
        request2.setDescription("Extra costs transaction");
        request2.setTaxRelevant(false);
        request2.setExtraCosts(true);

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isCreated());

        // Filter by tax relevant flag
        mockMvc.perform(get("/api/v1/transactions")
                .param("taxRelevant", "true"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].description").value("Tax relevant transaction"));

        // Filter by extra costs flag
        mockMvc.perform(get("/api/v1/transactions")
                .param("extraCosts", "true"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].description").value("Extra costs transaction"));
    }

    @Test
    void testGetTransactionsByAttributes() throws Exception {
        // Create transactions with different attributes
        CreateTransactionDataRequest request1 = new CreateTransactionDataRequest();
        request1.setDate(LocalDate.of(2024, 1, 15));
        request1.setAccountname("Checking Account");
        request1.setAmount(100000L);
        request1.setParticipant("John Doe");
        request1.setDescription("Payment to John");
        request1.setReferenceid(12345L);

        CreateTransactionDataRequest request2 = new CreateTransactionDataRequest();
        request2.setDate(LocalDate.of(2024, 1, 16));
        request2.setAccountname("Savings Account");
        request2.setAmount(200000L);
        request2.setParticipant("Jane Smith");
        request2.setDescription("Payment to Jane");
        request2.setReferenceid(12346L);

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isCreated());

        // Filter by account name
        mockMvc.perform(get("/api/v1/transactions")
                .param("accountname", "Checking"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].accountname").value("Checking Account"));

        // Filter by participant
        mockMvc.perform(get("/api/v1/transactions")
                .param("participant", "Jane"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].participant").value("Jane Smith"));

        // Filter by reference ID
        mockMvc.perform(get("/api/v1/transactions")
                .param("referenceid", "12345"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].referenceid").value(12345));
    }
}
