package com.mymueller.transactionsstore;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify H2 database configuration is working correctly
 */
@SpringBootTest
@ActiveProfiles("test")
class H2DatabaseConfigurationTest {

    @Autowired
    private DataSource dataSource;

    @Test
    void testDataSourceIsConfigured() {
        assertNotNull(dataSource, "DataSource should be configured");
    }

    @Test
    void testH2DatabaseConnection() throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            assertNotNull(connection, "Connection should not be null");
            assertFalse(connection.isClosed(), "Connection should be open");
            
            DatabaseMetaData metaData = connection.getMetaData();
            String databaseProductName = metaData.getDatabaseProductName();
            
            assertEquals("H2", databaseProductName, "Database should be H2");
            
            // Verify it's an in-memory database
            String url = metaData.getURL();
            assertTrue(url.contains("mem:"), "Should be using in-memory H2 database");
            assertTrue(url.contains("testdb"), "Should be using testdb database name");
        }
    }

    @Test
    void testDatabaseIsInMemory() throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            String url = metaData.getURL();

            // Verify H2 in-memory configuration
            assertTrue(url.startsWith("jdbc:h2:mem:"), "Should use H2 in-memory database (URL: " + url + ")");

            // The URL should contain either our configured database name or a Spring-generated one
            assertTrue(url.contains("testdb") || url.matches(".*jdbc:h2:mem:[a-f0-9-]+.*"),
                "Should use either our configured testdb or Spring's generated database name (URL: " + url + ")");
        }
    }

    @Test
    void testDatabaseCredentials() throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            String userName = metaData.getUserName();
            
            assertEquals("SA", userName.toUpperCase(), "Username should be SA");
        }
    }
}
