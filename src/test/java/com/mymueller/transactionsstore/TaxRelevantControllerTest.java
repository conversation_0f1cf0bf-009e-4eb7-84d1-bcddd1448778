package com.mymueller.transactionsstore;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mymueller.transactionsstore.controller.TaxRelevantController;
import com.mymueller.transactionsstore.controller.TransactionsController;
import com.mymueller.transactionsstore.model.CreateTransactionDataRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Test class for TaxRelevantController
 */
@WebMvcTest(controllers = {TaxRelevantController.class, TransactionsController.class})
@ActiveProfiles("test")
class TaxRelevantControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private TransactionsController transactionsController;

    @BeforeEach
    void setUp() {
        // Clear any existing transactions before each test
        transactionsController.clearTransactions();
    }

    @Test
    void testGetTaxRelevantEmpty() throws Exception {
        mockMvc.perform(get("/api/v1/taxrelevant"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));
    }

    @Test
    void testGetTaxRelevantWithData() throws Exception {
        // Create transactions with different tax relevant classifications
        CreateTransactionDataRequest request1 = new CreateTransactionDataRequest();
        request1.setDate(LocalDate.of(2024, 1, 15));
        request1.setAccountname("Account 1");
        request1.setAmount(100000L);
        request1.setParticipant("John Doe");
        request1.setDescription("Transaction 1");
        request1.setTaxRelevant("VAT applicable");

        CreateTransactionDataRequest request2 = new CreateTransactionDataRequest();
        request2.setDate(LocalDate.of(2024, 1, 16));
        request2.setAccountname("Account 2");
        request2.setAmount(200000L);
        request2.setParticipant("Jane Doe");
        request2.setDescription("Transaction 2");
        request2.setTaxRelevant("Tax exempt");

        CreateTransactionDataRequest request3 = new CreateTransactionDataRequest();
        request3.setDate(LocalDate.of(2024, 1, 17));
        request3.setAccountname("Account 3");
        request3.setAmount(300000L);
        request3.setParticipant("Bob Smith");
        request3.setDescription("Transaction 3");
        request3.setTaxRelevant("VAT applicable"); // Duplicate

        // Create the transactions
        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request3)))
                .andExpect(status().isCreated());

        // Test the tax relevant endpoint
        mockMvc.perform(get("/api/v1/taxrelevant"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2)) // Should have 2 distinct values
                .andExpect(jsonPath("$[0].classification").value("Tax exempt")) // Sorted alphabetically
                .andExpect(jsonPath("$[1].classification").value("VAT applicable"));
    }

    @Test
    void testGetTaxRelevantIgnoresNullAndEmpty() throws Exception {
        // Create transactions with null and empty tax relevant values
        CreateTransactionDataRequest request1 = new CreateTransactionDataRequest();
        request1.setDate(LocalDate.of(2024, 1, 15));
        request1.setAccountname("Account 1");
        request1.setAmount(100000L);
        request1.setParticipant("John Doe");
        request1.setDescription("Transaction 1");
        request1.setTaxRelevant("VAT applicable");

        CreateTransactionDataRequest request2 = new CreateTransactionDataRequest();
        request2.setDate(LocalDate.of(2024, 1, 16));
        request2.setAccountname("Account 2");
        request2.setAmount(200000L);
        request2.setParticipant("Jane Doe");
        request2.setDescription("Transaction 2");
        // taxRelevant is null

        CreateTransactionDataRequest request3 = new CreateTransactionDataRequest();
        request3.setDate(LocalDate.of(2024, 1, 17));
        request3.setAccountname("Account 3");
        request3.setAmount(300000L);
        request3.setParticipant("Bob Smith");
        request3.setDescription("Transaction 3");
        request3.setTaxRelevant(""); // Empty string

        // Create the transactions
        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request3)))
                .andExpect(status().isCreated());

        // Test the tax relevant endpoint - should only return the non-null, non-empty value
        mockMvc.perform(get("/api/v1/taxrelevant"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].classification").value("VAT applicable"));
    }
}
