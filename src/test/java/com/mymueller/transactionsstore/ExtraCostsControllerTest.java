package com.mymueller.transactionsstore;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mymueller.transactionsstore.controller.ExtraCostsController;
import com.mymueller.transactionsstore.controller.TransactionsController;
import com.mymueller.transactionsstore.model.CreateTransactionDataRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Test class for ExtraCostsController
 */
@WebMvcTest(controllers = {ExtraCostsController.class, TransactionsController.class})
@ActiveProfiles("test")
class ExtraCostsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private TransactionsController transactionsController;

    @BeforeEach
    void setUp() {
        // Clear any existing transactions before each test
        transactionsController.clearTransactions();
    }

    @Test
    void testGetExtraCostsEmpty() throws Exception {
        mockMvc.perform(get("/api/v1/extra-costs"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));
    }

    @Test
    void testGetExtraCostsWithData() throws Exception {
        // Create transactions with different extra costs classifications
        CreateTransactionDataRequest request1 = new CreateTransactionDataRequest();
        request1.setDate(LocalDate.of(2024, 1, 15));
        request1.setAccountname("Account 1");
        request1.setAmount(100000L);
        request1.setParticipant("John Doe");
        request1.setDescription("Transaction 1");
        request1.setExtraCosts("Shipping and handling fees");

        CreateTransactionDataRequest request2 = new CreateTransactionDataRequest();
        request2.setDate(LocalDate.of(2024, 1, 16));
        request2.setAccountname("Account 2");
        request2.setAmount(200000L);
        request2.setParticipant("Jane Doe");
        request2.setDescription("Transaction 2");
        request2.setExtraCosts("Processing charges");

        CreateTransactionDataRequest request3 = new CreateTransactionDataRequest();
        request3.setDate(LocalDate.of(2024, 1, 17));
        request3.setAccountname("Account 3");
        request3.setAmount(300000L);
        request3.setParticipant("Bob Smith");
        request3.setDescription("Transaction 3");
        request3.setExtraCosts("Shipping and handling fees"); // Duplicate

        // Create the transactions
        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request3)))
                .andExpect(status().isCreated());

        // Test the extra costs endpoint
        mockMvc.perform(get("/api/v1/extra-costs"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2)) // Should have 2 distinct values
                .andExpect(jsonPath("$[0].classification").value("Processing charges")) // Sorted alphabetically
                .andExpect(jsonPath("$[1].classification").value("Shipping and handling fees"));
    }

    @Test
    void testGetExtraCostsIgnoresNullAndEmpty() throws Exception {
        // Create transactions with null and empty extra costs values
        CreateTransactionDataRequest request1 = new CreateTransactionDataRequest();
        request1.setDate(LocalDate.of(2024, 1, 15));
        request1.setAccountname("Account 1");
        request1.setAmount(100000L);
        request1.setParticipant("John Doe");
        request1.setDescription("Transaction 1");
        request1.setExtraCosts("Processing charges");

        CreateTransactionDataRequest request2 = new CreateTransactionDataRequest();
        request2.setDate(LocalDate.of(2024, 1, 16));
        request2.setAccountname("Account 2");
        request2.setAmount(200000L);
        request2.setParticipant("Jane Doe");
        request2.setDescription("Transaction 2");
        // extraCosts is null

        CreateTransactionDataRequest request3 = new CreateTransactionDataRequest();
        request3.setDate(LocalDate.of(2024, 1, 17));
        request3.setAccountname("Account 3");
        request3.setAmount(300000L);
        request3.setParticipant("Bob Smith");
        request3.setDescription("Transaction 3");
        request3.setExtraCosts(""); // Empty string

        // Create the transactions
        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request3)))
                .andExpect(status().isCreated());

        // Test the extra costs endpoint - should only return the non-null, non-empty value
        mockMvc.perform(get("/api/v1/extra-costs"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].classification").value("Processing charges"));
    }
}
