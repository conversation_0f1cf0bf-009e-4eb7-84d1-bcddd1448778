package com.mymueller.transactionsstore;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
class TransactionsstoreApplicationTests {

	@Autowired
	private ApplicationContext applicationContext;

	@Autowired
	private DataSource dataSource;

	@Test
	void contextLoads() {
		assertNotNull(applicationContext, "Application context should load");
	}

	@Test
	void testH2DatabaseIsAvailableInApplicationContext() throws SQLException {
		assertNotNull(dataSource, "DataSource should be available in context");

		try (Connection connection = dataSource.getConnection()) {
			assertNotNull(connection, "Should be able to get database connection");
			assertFalse(connection.isClosed(), "Connection should be open");
		}
	}

	@Test
	void testApplicationStartsWithH2Database() {
		// This test verifies that the entire Spring Boot application can start
		// with H2 database configuration without any errors
		assertTrue(applicationContext.containsBean("dataSource"), "DataSource bean should exist");
	}
}
