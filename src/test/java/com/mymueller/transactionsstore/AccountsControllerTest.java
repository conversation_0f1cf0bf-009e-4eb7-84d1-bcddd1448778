package com.mymueller.transactionsstore;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mymueller.transactionsstore.controller.AccountsController;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

/**
 * Tests for the AccountsController
 */
@WebMvcTest(controllers = {AccountsController.class})
@ActiveProfiles("test")
class AccountsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AccountsController accountsController;

    @BeforeEach
    void setUp() {
        // Clear accounts before each test
//        accountsController.clearAccounts();
    }

}
