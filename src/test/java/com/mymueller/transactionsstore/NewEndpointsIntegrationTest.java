package com.mymueller.transactionsstore;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mymueller.transactionsstore.controller.TransactionsController;
import com.mymueller.transactionsstore.model.CreateTransactionDataRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDate;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration test for the new tax relevant and extra costs endpoints
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
class NewEndpointsIntegrationTest {

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private TransactionsController transactionsController;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        // Clear any existing transactions before each test
        transactionsController.clearTransactions();
    }

    @Test
    void testCompleteWorkflowWithNewEndpoints() throws Exception {
        // Create some test transactions with different tax relevant and extra costs values
        CreateTransactionDataRequest request1 = new CreateTransactionDataRequest();
        request1.setDate(LocalDate.of(2024, 1, 15));
        request1.setAccountname("Business Account");
        request1.setAmount(100000L);
        request1.setParticipant("Supplier A");
        request1.setDescription("Office supplies");
        request1.setTaxRelevant("VAT applicable");
        request1.setExtraCosts("Shipping and handling fees");

        CreateTransactionDataRequest request2 = new CreateTransactionDataRequest();
        request2.setDate(LocalDate.of(2024, 1, 16));
        request2.setAccountname("Personal Account");
        request2.setAmount(50000L);
        request2.setParticipant("Freelancer B");
        request2.setDescription("Consulting services");
        request2.setTaxRelevant("Tax exempt");
        request2.setExtraCosts("Processing charges");

        CreateTransactionDataRequest request3 = new CreateTransactionDataRequest();
        request3.setDate(LocalDate.of(2024, 1, 17));
        request3.setAccountname("Investment Account");
        request3.setAmount(200000L);
        request3.setParticipant("Investment Firm");
        request3.setDescription("Stock purchase");
        request3.setTaxRelevant("Capital gains tax");
        request3.setExtraCosts("Brokerage fees");

        // Create the transactions
        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/transactions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request3)))
                .andExpect(status().isCreated());

        // Test the tax relevant endpoint
        mockMvc.perform(get("/api/v1/taxrelevant"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(3))
                .andExpect(jsonPath("$[0].classification").value("Capital gains tax"))
                .andExpect(jsonPath("$[1].classification").value("Tax exempt"))
                .andExpect(jsonPath("$[2].classification").value("VAT applicable"));

        // Test the extra costs endpoint
        mockMvc.perform(get("/api/v1/extracosts"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(3))
                .andExpect(jsonPath("$[0].classification").value("Brokerage fees"))
                .andExpect(jsonPath("$[1].classification").value("Processing charges"))
                .andExpect(jsonPath("$[2].classification").value("Shipping and handling fees"));

        // Test filtering transactions by tax relevant
        mockMvc.perform(get("/api/v1/transactions")
                .param("taxRelevant", "VAT"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].description").value("Office supplies"));

        // Test filtering transactions by extra costs
        mockMvc.perform(get("/api/v1/transactions")
                .param("extraCosts", "processing"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].description").value("Consulting services"));
    }
}
