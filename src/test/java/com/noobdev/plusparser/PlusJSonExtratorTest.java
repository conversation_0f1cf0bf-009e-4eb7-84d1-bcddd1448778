package com.noobdev.plusparser;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Optional;

public class PlusJSonExtratorTest
{
    PlusJSonExtrator jsonExtractor;

    @BeforeEach
    void prepareService()
    {
        jsonExtractor = new PlusJSonExtrator();
        Assertions.assertNotNull(jsonExtractor);
    }

    @AfterEach
    void cleanupService()
    {
        jsonExtractor = null;
    }

    @Test
    void testMissingMarkers()
    {
        Optional<String> result = jsonExtractor.extractJson("simple test");

        Assertions.assertNotNull(result);

        Assertions.assertTrue(result.isEmpty());
    }

    @Test
    void testTextMarkers()
    {
        Optional<String> result = jsonExtractor.extractJson(";Fusion.globalContent={testContent};Fusion.globalContentConfig");

        Assertions.assertNotNull(result);

        Assertions.assertTrue(result.isPresent());

        Assertions.assertEquals("{testContent}", result.get());
    }
}
