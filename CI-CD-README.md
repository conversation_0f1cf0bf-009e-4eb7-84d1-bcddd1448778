# GitLab CI/CD Pipeline Documentation

## Overview

This project uses a GitLab CI/CD pipeline with three main stages:

1. **Build** - Compiles the Java Spring Boot application using Gradle
2. **Test** - Runs JUnit tests to ensure code quality
3. **Docker** - Creates and pushes a small, optimized Docker image

## Pipeline Stages

### Build Stage
- Uses `eclipse-temurin:21-jdk-alpine` image
- Runs `./gradlew clean assemble`
- Creates artifacts (JAR files) that are passed to subsequent stages
- Caches Gradle dependencies for faster builds

### Test Stage
- Uses `eclipse-temurin:21-jdk-alpine` image
- Runs `./gradlew test jacocoTestReport`
- Generates test reports and coverage information
- Artifacts include test results, HTML reports, and coverage data
- GitLab displays test results directly in the UI

### Docker Stage
- Uses multi-stage Docker build for smaller final image
- Only runs after successful build and test stages
- Pushes image tagged with branch/tag name
- Additionally tags as `latest` when building from `main` branch
- Uses Docker-in-Docker (DinD) service

## Docker Image Features

The Docker image is optimized for production use:

- **Multi-stage build**: Separates build environment from runtime
- **Small size**: Uses JRE instead of JDK for runtime
- **Security**: Runs as non-root user
- **Health checks**: Includes health check endpoint via Spring Actuator
- **Proper layering**: Optimized for Docker layer caching

## Environment Variables

The pipeline uses several GitLab CI variables:

- `CI_REGISTRY_IMAGE` - GitLab container registry image path
- `CI_REGISTRY_USER` - GitLab registry username
- `CI_REGISTRY_PASSWORD` - GitLab registry password
- `CI_COMMIT_REF_SLUG` - Branch/tag name for image tagging

## Running Locally

### Build the application:
```bash
./gradlew clean build
```

### Run tests:
```bash
./gradlew test
```

### Build Docker image:
```bash
docker build -t plusparser:local .
```

### Run Docker container:
```bash
docker run -p 7071:7071 plusparser:local
```

## Pipeline Triggers

The pipeline runs on:
- All branch pushes
- Tag creation
- Merge requests (build and test only)

Docker images are only built and pushed for:
- Branch pushes (not merge requests)
- Tag creation

## Caching

The pipeline uses GitLab CI cache for:
- Gradle wrapper
- Gradle dependencies

This significantly speeds up subsequent builds.

## Test Results Display

GitLab provides excellent test result visualization:

### 📊 **Test Reports Tab**
- Navigate to **CI/CD > Jobs > [Test Job] > Tests** tab
- Shows passed/failed/skipped test counts
- Lists individual test cases with status
- Displays test execution time
- Shows failure details and stack traces

### 📈 **Coverage Reports**
- Navigate to **CI/CD > Jobs > [Test Job] > Coverage** tab
- Shows overall coverage percentage
- Line-by-line coverage visualization
- Coverage trends over time
- Downloadable HTML coverage reports

### 🔍 **Merge Request Integration**
- Test results appear directly in merge request discussions
- Coverage changes are highlighted
- Failed tests block merge (if configured)
- Test status badges in merge request UI

### 📁 **Downloadable Artifacts**
Available for 1 week after job completion:
- `build/reports/tests/test/` - HTML test reports
- `build/test-results/test/` - JUnit XML results
- `build/reports/jacoco/test/html/` - Coverage HTML reports
- `build/reports/jacoco/test/jacocoTestReport.xml` - Coverage XML

### 🎯 **Quick Access**
- **Pipeline view**: Shows test count and status
- **Job logs**: Displays test summary in console
- **Badges**: Add test/coverage badges to README
- **API**: Access test data via GitLab API

### 📧 **Notifications**
- Email notifications for test failures
- Slack/Teams integration available
- Custom webhooks for test events

## Health Checks

The Docker image includes health checks that verify:
- Application is running
- Spring Actuator health endpoint responds

**Security**: Management endpoints run on a separate internal port (8081) that is not exposed outside the container.

- **Application port**: 7071 (exposed)
- **Management port**: 8081 (internal only)
- **Health check endpoint**: `http://localhost:8081/actuator/health` (internal)

## Troubleshooting

### Common Issues:

1. **Build fails**: Check Gradle build logs in the build stage
2. **Tests fail**: Review test reports in artifacts
3. **Docker build fails**: Verify Dockerfile syntax and dependencies
4. **Registry push fails**: Check GitLab registry permissions

### Debugging:

- Download artifacts from failed jobs to review logs
- Use GitLab CI/CD variables to debug environment issues
- Check Docker service logs for Docker-related issues
