# Management Port Configuration

## Overview

The Transaction Store application is configured to run Spring Boot Actuator management endpoints on a separate port from the main application. This provides better security and operational separation.

## Port Configuration

### Production Configuration

- **Application Port**: `8080` - Main REST API endpoints
- **Management Port**: `8081` - Actuator endpoints (health, metrics, info)
- **Management Address**: `127.0.0.1` - Only accessible locally for security

### Test Configuration

- **Application Port**: Random port (0) - Assigned by Spring Boot
- **Management Port**: Random port (0) - Assigned by Spring Boot
- **Management Address**: `127.0.0.1` - Local access only

## Available Endpoints

### Application Endpoints (Port 8080)

```
GET  /api/v1/accounts           - Get all accounts
POST /api/v1/accounts           - Create account
GET  /api/v1/accounts/{id}      - Get account by ID
PUT  /api/v1/accounts/{id}      - Update account

GET  /api/v1/participants       - Get all participants

GET  /api/v1/transactions       - Get all transactions (with filtering)
POST /api/v1/transactions       - Create transaction
GET  /api/v1/transactions/{id}  - Get transaction by ID
PUT  /api/v1/transactions/{id}  - Update transaction
DELETE /api/v1/transactions/{id} - Delete transaction
```

### Management Endpoints (Port 8081)

```
GET  /actuator/health           - Application health status
GET  /actuator/info             - Application information
GET  /actuator/metrics          - Application metrics
GET  /actuator/metrics/{name}   - Specific metric
```

## Security Benefits

### Separation of Concerns

1. **Application Traffic** (Port 8080):
   - Business logic endpoints
   - User-facing API
   - Can be exposed to external networks
   - Subject to authentication/authorization

2. **Management Traffic** (Port 8081):
   - Monitoring and health checks
   - Operational metrics
   - Internal use only (127.0.0.1)
   - Can have different security policies

### Network Security

- **Firewall Rules**: Different ports can have different firewall rules
- **Load Balancer**: Health checks can use management port
- **Monitoring**: Prometheus/Grafana can access metrics port separately
- **Access Control**: Management endpoints restricted to localhost

## Docker Configuration

### Secure Port Exposure (Recommended)

```dockerfile
# Expose only application port (management port stays internal)
EXPOSE 8080

# Health check uses internal management port (not exposed externally)
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8081/actuator/health || exit 1
```

### Alternative: Expose Management Port (Less Secure)

```dockerfile
# Only if you need external access to management endpoints
EXPOSE 8080 8081

# Same health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8081/actuator/health || exit 1
```

### Docker Compose Example

```yaml
version: '3.8'
services:
  transactionstore:
    image: transactionstore:latest
    ports:
      - "8080:8080"    # Application port
      - "8081:8081"    # Management port (optional, for monitoring)
    environment:
      - SPRING_PROFILES_ACTIVE=production
```

## Kubernetes Configuration

### Service Definition

```yaml
apiVersion: v1
kind: Service
metadata:
  name: transactionstore
spec:
  selector:
    app: transactionstore
  ports:
    - name: http
      port: 8080
      targetPort: 8080
    - name: management
      port: 8081
      targetPort: 8081
  type: ClusterIP
```

### Health Check Configuration

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: transactionstore
spec:
  template:
    spec:
      containers:
      - name: transactionstore
        image: transactionstore:latest
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8081
          name: management
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
```

## Monitoring Integration

### Prometheus Configuration

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'transactionstore'
    static_configs:
      - targets: ['localhost:8081']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s
```

### Health Check Scripts

```bash
#!/bin/bash
# health-check.sh

# Check application health
curl -f http://localhost:8081/actuator/health || exit 1

# Check specific health indicators
curl -f http://localhost:8081/actuator/health/db || exit 1
```

## Configuration Properties

### application.yml

```yaml
# Main application configuration
server:
  port: 8080

# Management configuration
management:
  server:
    port: 8081
    address: 127.0.0.1  # Restrict to localhost
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
  health:
    defaults:
      enabled: true
```

### Environment Variables

```bash
# Override management port
MANAGEMENT_SERVER_PORT=9090

# Override management address
MANAGEMENT_SERVER_ADDRESS=0.0.0.0

# Enable additional endpoints
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics,prometheus,env
```

## Testing

### Unit Tests

The `ManagementPortTest` class verifies:
- Management port is different from application port
- Health endpoints work on management port
- Application endpoints work on application port
- Cross-port access is properly restricted

### Manual Testing

```bash
# Test application endpoints
curl http://localhost:8080/api/v1/accounts

# Test management endpoints
curl http://localhost:8081/actuator/health
curl http://localhost:8081/actuator/metrics

# Verify separation (should fail)
curl http://localhost:8080/actuator/health  # 404
curl http://localhost:8081/api/v1/accounts  # 404
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**: Ensure ports 8080 and 8081 are available
2. **Firewall**: Management port might be blocked by firewall
3. **Docker**: Both ports must be exposed in Dockerfile
4. **Kubernetes**: Service must define both ports

### Debugging

```bash
# Check which ports are in use
netstat -tulpn | grep :808

# Test connectivity
telnet localhost 8080
telnet localhost 8081

# Check application logs
docker logs transactionstore | grep "management"
```

## Production Considerations

### Security

- Keep management port on private network only
- Use authentication for management endpoints in production
- Consider VPN access for management endpoints
- Monitor access to management endpoints

### Performance

- Management endpoints have minimal performance impact
- Metrics collection can be tuned for frequency
- Health checks should be lightweight

### Monitoring

- Set up alerts for health check failures
- Monitor management endpoint response times
- Track metrics endpoint usage
