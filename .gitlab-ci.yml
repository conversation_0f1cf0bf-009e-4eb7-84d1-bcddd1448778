# GitLab CI/CD Pipeline for Transaction Store Application
# This pipeline builds, tests, and packages the Spring Boot application

# Define the stages in order of execution
stages:
  - build
  - test
  - package

# Global variables
variables:
  # Use the official Gradle image with JDK 21
  GRADLE_OPTS: "-Dorg.gradle.daemon=false -Dorg.gradle.caching=true"
  GRADLE_USER_HOME: "$CI_PROJECT_DIR/.gradle"
  # Docker variables for future use
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

# Cache configuration to speed up builds
cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - .gradle/wrapper
    - .gradle/caches
    - build/

# ============================================================================
# BUILD STAGE
# ============================================================================

build:
  stage: build
  image: gradle:8.14-jdk21
  script:
    - echo "🏗️ Starting build stage..."
    - echo "Java version:"
    - java -version
    - echo "Gradle version:"
    - gradle --version
    - echo "🔧 Generating OpenAPI code..."
    - ./gradlew openApiGenerate --info
    - echo "🏗️ Compiling application..."
    - ./gradlew compileJava --info
    - echo "✅ Build completed successfully!"
  artifacts:
    name: "build-artifacts-$CI_COMMIT_SHORT_SHA"
    expire_in: 1 hour
    paths:
      - build/generated/
      - build/classes/
      - build/resources/
    reports:
      # Store build information for GitLab
      junit: build/test-results/test/TEST-*.xml
    when: always
  rules:
    - when: always

# ============================================================================
# TEST STAGE
# ============================================================================

test:
  stage: test
  image: gradle:8.14-jdk21
  dependencies:
    - build
  script:
    - echo "🧪 Starting test stage..."
    - echo "🔍 Running tests with coverage..."
    - ./gradlew test jacocoTestReport --info
    - echo "📊 Generating coverage summary..."
    - ./gradlew coverageSummary
    - echo "✅ Verifying coverage thresholds..."
    - ./gradlew jacocoTestCoverageVerification
    - echo "🎉 All tests passed with sufficient coverage!"
  artifacts:
    name: "test-artifacts-$CI_COMMIT_SHORT_SHA"
    expire_in: 1 week
    paths:
      - build/reports/tests/test/
      - build/reports/jacoco/test/
      - build/test-results/test/
    reports:
      junit: build/test-results/test/TEST-*.xml
      coverage_report:
        coverage_format: jacoco
        path: build/reports/jacoco/test/jacocoTestReport.xml
    when: always
  coverage: '/Instructions: \d+\/\d+ \((\d+,?\d*)%\)/'
  rules:
    - when: always

# ============================================================================
# PACKAGE STAGE
# ============================================================================

package:
  stage: package
  image: gradle:8.14-jdk21
  dependencies:
    - build
    - test
  script:
    - echo "📦 Starting package stage..."
    - echo "🏗️ Building application JAR..."
    - ./gradlew bootJar --info
    - echo "📋 Application JAR details:"
    - ls -la build/libs/
    - echo "✅ JAR file created successfully"
    - echo "🐳 Verifying Docker files exist..."
    - ls -la Dockerfile .dockerignore
    - echo "📦 Package stage completed successfully!"
  artifacts:
    name: "package-artifacts-$CI_COMMIT_SHORT_SHA"
    expire_in: 1 week
    paths:
      - build/libs/*.jar
      - Dockerfile
      - .dockerignore
    when: always
  rules:
    - when: always

# ============================================================================
# ADDITIONAL JOBS (Optional - disabled with . prefix)
# ============================================================================

# Code quality analysis (can be enabled by removing . prefix)
.code_quality:
  stage: test
  image: gradle:8.14-jdk21
  dependencies:
    - build
  script:
    - echo "🔍 Running code quality analysis..."
    - ./gradlew check --info
  artifacts:
    reports:
      codequality: build/reports/detekt/detekt.xml
  rules:
    - when: manual

# Security scanning (can be enabled by removing . prefix)
.security_scan:
  stage: test
  image: gradle:8.14-jdk21
  dependencies:
    - build
  script:
    - echo "🔒 Running security scan..."
    - ./gradlew dependencyCheckAnalyze --info
  artifacts:
    reports:
      dependency_scanning: build/reports/dependency-check-report.xml
  rules:
    - when: manual
