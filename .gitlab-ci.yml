# GitLab CI/CD Pipeline for Spring Boot Application
image: docker:24.0.5

variables:
  # Docker configuration
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_HOST: tcp://docker:2376
  
  # Image naming
  CONTAINER_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG
  CONTAINER_IMAGE_LATEST: $CI_REGISTRY_IMAGE:latest
  
  # Gradle configuration
  GRADLE_OPTS: "-Dorg.gradle.daemon=false"
  GRADLE_USER_HOME: "$CI_PROJECT_DIR/.gradle"

# Define pipeline stages
stages:
  - build
  - test
  - docker

# Cache configuration for faster builds
cache:
  paths:
    - .gradle/wrapper
    - .gradle/caches

# Build stage - compile the application
build:
  stage: build
  image: eclipse-temurin:21-jdk-alpine
  before_script:
    - chmod +x gradlew
  script:
    - echo "Building application..."
    - ./gradlew clean assemble --build-cache --no-daemon
    - echo "Build completed successfully"
  artifacts:
    paths:
      - build/libs/*.jar
    expire_in: 1 hour
    reports:
      junit: build/test-results/test/**/TEST-*.xml
  only:
    - branches
    - tags
    - merge_requests

# Test stage - run unit tests
test:
  stage: test
  image: eclipse-temurin:21-jdk-alpine
  before_script:
    - chmod +x gradlew
  script:
    - echo "Running tests with coverage..."
    - ./gradlew test jacocoTestReport --build-cache --no-daemon
    - echo "Tests completed successfully"
    # Display test summary
    - echo "=== Test Summary ==="
    - find build/test-results/test -name "*.xml" -exec grep -l "testcase" {} \; | wc -l | xargs echo "Test files:"
    - find build/test-results/test -name "*.xml" -exec grep -o "testcase" {} \; | wc -l | xargs echo "Total tests:"
    - find build/test-results/test -name "*.xml" -exec grep -o 'failure\|error' {} \; | wc -l | xargs echo "Failed tests:"
  artifacts:
    when: always
    paths:
      - build/reports/tests/test/
      - build/test-results/test/
      - build/reports/jacoco/test/html/
      - build/reports/jacoco/test/jacocoTestReport.xml
    expire_in: 1 week
    reports:
      junit: build/test-results/test/**/TEST-*.xml
      coverage_report:
        coverage_format: jacoco
        path: build/reports/jacoco/test/jacocoTestReport.xml
  coverage: '/Total.*?([0-9]{1,3})%/'
  only:
    - branches
    - tags
    - merge_requests

# Docker stage - build and push container image
docker:
  stage: docker
  services:
    - docker:24.0.5-dind
  before_script:
    - echo "Logging into GitLab Container Registry..."
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - echo "Building Docker image..."
    - docker build --pull -t $CONTAINER_IMAGE .
    - echo "Pushing Docker image to registry..."
    - docker push $CONTAINER_IMAGE
    # Tag and push as latest if on main branch
    - |
      if [ "$CI_COMMIT_REF_NAME" = "main" ]; then
        echo "Tagging and pushing as latest..."
        docker tag $CONTAINER_IMAGE $CONTAINER_IMAGE_LATEST
        docker push $CONTAINER_IMAGE_LATEST
      fi
    - echo "Docker image pushed successfully"
  dependencies:
    - build
    - test
  only:
    - branches
    - tags
  except:
    - merge_requests

# Optional: Deploy stage (commented out - uncomment and configure as needed)
# deploy:
#   stage: deploy
#   script:
#     - echo "Deploying application..."
#     # Add your deployment commands here
#   environment:
#     name: production
#     url: https://your-app-url.com
#   only:
#     - main
#   when: manual
