#!/bin/bash

# GitLab CI/CD Pipeline Validation Script
# This script simulates the GitLab pipeline stages locally

set -e  # Exit on any error

echo "🚀 GitLab CI/CD Pipeline Validation"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print stage headers
print_stage() {
    echo ""
    echo -e "${BLUE}============================================================================${NC}"
    echo -e "${BLUE}🎯 STAGE: $1${NC}"
    echo -e "${BLUE}============================================================================${NC}"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to print warning
print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command -v java &> /dev/null; then
    print_error "Java is not installed or not in PATH"
    exit 1
fi

if ! command -v ./gradlew &> /dev/null; then
    print_error "Gradle wrapper not found"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
echo "Java version: $JAVA_VERSION"

if [[ ! "$JAVA_VERSION" =~ ^(17|21) ]]; then
    print_warning "Java version should be 17 or 21 for best compatibility"
fi

print_success "Prerequisites check completed"

# ============================================================================
# BUILD STAGE
# ============================================================================

print_stage "BUILD"

echo "🏗️ Starting build stage..."

echo "🔧 Generating OpenAPI code..."
if ./gradlew openApiGenerate --quiet; then
    print_success "OpenAPI code generation completed"
else
    print_error "OpenAPI code generation failed"
    exit 1
fi

echo "🏗️ Compiling application..."
if ./gradlew compileJava --quiet; then
    print_success "Application compilation completed"
else
    print_error "Application compilation failed"
    exit 1
fi

# Check build artifacts
if [ -d "build/generated" ]; then
    print_success "Generated code artifacts created"
else
    print_error "Generated code artifacts missing"
    exit 1
fi

if [ -d "build/classes" ]; then
    print_success "Compiled classes artifacts created"
else
    print_error "Compiled classes artifacts missing"
    exit 1
fi

print_success "Build stage completed successfully!"

# ============================================================================
# TEST STAGE
# ============================================================================

print_stage "TEST"

echo "🧪 Starting test stage..."

echo "🔍 Running tests with coverage..."
if ./gradlew test jacocoTestReport --quiet; then
    print_success "Tests executed successfully"
else
    print_error "Tests failed"
    exit 1
fi

echo "📊 Generating coverage summary..."
if ./gradlew coverageSummary --quiet; then
    print_success "Coverage summary generated"
else
    print_error "Coverage summary generation failed"
    exit 1
fi

echo "✅ Verifying coverage thresholds..."
if ./gradlew jacocoTestCoverageVerification --quiet; then
    print_success "Coverage thresholds met"
else
    print_error "Coverage thresholds not met"
    exit 1
fi

# Check test artifacts
if [ -d "build/reports/tests/test" ]; then
    print_success "Test reports created"
else
    print_error "Test reports missing"
    exit 1
fi

if [ -d "build/reports/jacoco/test" ]; then
    print_success "Coverage reports created"
else
    print_error "Coverage reports missing"
    exit 1
fi

if [ -f "build/reports/jacoco/test/jacocoTestReport.xml" ]; then
    print_success "JaCoCo XML report created"
else
    print_error "JaCoCo XML report missing"
    exit 1
fi

print_success "Test stage completed successfully!"

# ============================================================================
# PACKAGE STAGE
# ============================================================================

print_stage "PACKAGE"

echo "📦 Starting package stage..."

echo "🏗️ Building application JAR..."
if ./gradlew bootJar --quiet; then
    print_success "Application JAR built successfully"
else
    print_error "Application JAR build failed"
    exit 1
fi

# Check JAR file
JAR_FILE=$(find build/libs -name "*.jar" -type f | head -n 1)
if [ -f "$JAR_FILE" ]; then
    JAR_SIZE=$(du -h "$JAR_FILE" | cut -f1)
    print_success "JAR file created: $JAR_FILE ($JAR_SIZE)"
else
    print_error "JAR file not found"
    exit 1
fi

echo "🐳 Checking Docker preparation..."
if [ -f ".gitlab-ci.yml" ]; then
    print_success "GitLab CI configuration found"
else
    print_error "GitLab CI configuration missing"
    exit 1
fi

# Simulate Docker file creation (as done in pipeline)
echo "📄 Creating Dockerfile..."
cat > Dockerfile.test << 'EOF'
FROM openjdk:21-jre-slim
WORKDIR /app
COPY build/libs/*.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "app.jar"]
EOF

if [ -f "Dockerfile.test" ]; then
    print_success "Dockerfile created successfully"
    rm Dockerfile.test  # Clean up test file
else
    print_error "Dockerfile creation failed"
    exit 1
fi

print_success "Package stage completed successfully!"

# ============================================================================
# SUMMARY
# ============================================================================

print_stage "SUMMARY"

echo "🎉 Pipeline validation completed successfully!"
echo ""
echo "📊 Validation Results:"
echo "  ✅ Build stage: PASSED"
echo "  ✅ Test stage: PASSED"
echo "  ✅ Package stage: PASSED"
echo ""
echo "📁 Artifacts created:"
echo "  📦 JAR file: $JAR_FILE"
echo "  📊 Test reports: build/reports/tests/test/"
echo "  📈 Coverage reports: build/reports/jacoco/test/"
echo ""
echo "🚀 Ready for GitLab CI/CD deployment!"
echo ""
echo "Next steps:"
echo "  1. Commit .gitlab-ci.yml to your repository"
echo "  2. Push to GitLab to trigger the pipeline"
echo "  3. Monitor pipeline execution in GitLab UI"
echo "  4. Review coverage reports in merge requests"
echo ""
echo "===================================="
echo "✅ All pipeline stages validated successfully!"
