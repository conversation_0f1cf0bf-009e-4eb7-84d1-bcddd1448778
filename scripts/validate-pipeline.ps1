# GitLab CI/CD Pipeline Validation Script (PowerShell)
# This script simulates the GitLab pipeline stages locally

param(
    [switch]$Quiet = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to print colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    if (-not $Quiet) {
        switch ($Color) {
            "Red" { Write-Host $Message -ForegroundColor Red }
            "Green" { Write-Host $Message -ForegroundColor Green }
            "Yellow" { Write-Host $Message -ForegroundColor Yellow }
            "Blue" { Write-Host $Message -ForegroundColor Blue }
            "Cyan" { Write-Host $Message -ForegroundColor Cyan }
            default { Write-Host $Message }
        }
    }
}

# Function to print stage headers
function Write-Stage {
    param([string]$StageName)
    
    Write-ColorOutput "" 
    Write-ColorOutput "============================================================================" "Blue"
    Write-ColorOutput "🎯 STAGE: $StageName" "Blue"
    Write-ColorOutput "============================================================================" "Blue"
}

# Function to print success
function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" "Green"
}

# Function to print error
function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" "Red"
}

# Function to print warning
function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️ $Message" "Yellow"
}

# Main script
try {
    Write-ColorOutput "🚀 GitLab CI/CD Pipeline Validation" "Cyan"
    Write-ColorOutput "====================================" "Cyan"

    # Check prerequisites
    Write-ColorOutput "🔍 Checking prerequisites..."

    # Check Java
    try {
        $javaVersion = & java -version 2>&1 | Select-Object -First 1
        Write-ColorOutput "Java version: $javaVersion"
        
        if ($javaVersion -notmatch "17|21") {
            Write-Warning "Java version should be 17 or 21 for best compatibility"
        }
    }
    catch {
        Write-Error "Java is not installed or not in PATH"
        exit 1
    }

    # Check Gradle wrapper
    if (-not (Test-Path ".\gradlew.bat")) {
        Write-Error "Gradle wrapper not found"
        exit 1
    }

    Write-Success "Prerequisites check completed"

    # ============================================================================
    # BUILD STAGE
    # ============================================================================

    Write-Stage "BUILD"

    Write-ColorOutput "🏗️ Starting build stage..."

    Write-ColorOutput "🔧 Generating OpenAPI code..."
    $result = & .\gradlew.bat openApiGenerate --quiet
    if ($LASTEXITCODE -eq 0) {
        Write-Success "OpenAPI code generation completed"
    } else {
        Write-Error "OpenAPI code generation failed"
        exit 1
    }

    Write-ColorOutput "🏗️ Compiling application..."
    $result = & .\gradlew.bat compileJava --quiet
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Application compilation completed"
    } else {
        Write-Error "Application compilation failed"
        exit 1
    }

    # Check build artifacts
    if (Test-Path "build\generated") {
        Write-Success "Generated code artifacts created"
    } else {
        Write-Error "Generated code artifacts missing"
        exit 1
    }

    if (Test-Path "build\classes") {
        Write-Success "Compiled classes artifacts created"
    } else {
        Write-Error "Compiled classes artifacts missing"
        exit 1
    }

    Write-Success "Build stage completed successfully!"

    # ============================================================================
    # TEST STAGE
    # ============================================================================

    Write-Stage "TEST"

    Write-ColorOutput "🧪 Starting test stage..."

    Write-ColorOutput "🔍 Running tests with coverage..."
    $result = & .\gradlew.bat test jacocoTestReport --quiet
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Tests executed successfully"
    } else {
        Write-Error "Tests failed"
        exit 1
    }

    Write-ColorOutput "📊 Generating coverage summary..."
    $result = & .\gradlew.bat coverageSummary --quiet
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Coverage summary generated"
    } else {
        Write-Error "Coverage summary generation failed"
        exit 1
    }

    Write-ColorOutput "✅ Verifying coverage thresholds..."
    $result = & .\gradlew.bat jacocoTestCoverageVerification --quiet
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Coverage thresholds met"
    } else {
        Write-Error "Coverage thresholds not met"
        exit 1
    }

    # Check test artifacts
    if (Test-Path "build\reports\tests\test") {
        Write-Success "Test reports created"
    } else {
        Write-Error "Test reports missing"
        exit 1
    }

    if (Test-Path "build\reports\jacoco\test") {
        Write-Success "Coverage reports created"
    } else {
        Write-Error "Coverage reports missing"
        exit 1
    }

    if (Test-Path "build\reports\jacoco\test\jacocoTestReport.xml") {
        Write-Success "JaCoCo XML report created"
    } else {
        Write-Error "JaCoCo XML report missing"
        exit 1
    }

    Write-Success "Test stage completed successfully!"

    # ============================================================================
    # PACKAGE STAGE
    # ============================================================================

    Write-Stage "PACKAGE"

    Write-ColorOutput "📦 Starting package stage..."

    Write-ColorOutput "🏗️ Building application JAR..."
    $result = & .\gradlew.bat bootJar --quiet
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Application JAR built successfully"
    } else {
        Write-Error "Application JAR build failed"
        exit 1
    }

    # Check JAR file
    $jarFile = Get-ChildItem "build\libs\*.jar" | Select-Object -First 1
    if ($jarFile) {
        $jarSize = [math]::Round($jarFile.Length / 1MB, 2)
        Write-Success "JAR file created: $($jarFile.Name) ($jarSize MB)"
    } else {
        Write-Error "JAR file not found"
        exit 1
    }

    Write-ColorOutput "🐳 Checking Docker preparation..."
    if (Test-Path ".gitlab-ci.yml") {
        Write-Success "GitLab CI configuration found"
    } else {
        Write-Error "GitLab CI configuration missing"
        exit 1
    }

    # Simulate Docker file creation (as done in pipeline)
    Write-ColorOutput "📄 Creating Dockerfile..."
    "FROM openjdk:21-jre-slim" | Out-File -FilePath "Dockerfile.test" -Encoding UTF8
    
    if (Test-Path "Dockerfile.test") {
        Write-Success "Dockerfile created successfully"
        Remove-Item "Dockerfile.test"  # Clean up test file
    } else {
        Write-Error "Dockerfile creation failed"
        exit 1
    }

    Write-Success "Package stage completed successfully!"

    # ============================================================================
    # SUMMARY
    # ============================================================================

    Write-Stage "SUMMARY"

    Write-ColorOutput "🎉 Pipeline validation completed successfully!" "Green"
    Write-ColorOutput ""
    Write-ColorOutput "📊 Validation Results:"
    Write-ColorOutput "  ✅ Build stage: PASSED" "Green"
    Write-ColorOutput "  ✅ Test stage: PASSED" "Green"
    Write-ColorOutput "  ✅ Package stage: PASSED" "Green"
    Write-ColorOutput ""
    Write-ColorOutput "📁 Artifacts created:"
    Write-ColorOutput "  📦 JAR file: $($jarFile.FullName)"
    Write-ColorOutput "  📊 Test reports: build\reports\tests\test\"
    Write-ColorOutput "  📈 Coverage reports: build\reports\jacoco\test\"
    Write-ColorOutput ""
    Write-ColorOutput "🚀 Ready for GitLab CI/CD deployment!" "Cyan"
    Write-ColorOutput ""
    Write-ColorOutput "Next steps:"
    Write-ColorOutput "  1. Commit .gitlab-ci.yml to your repository"
    Write-ColorOutput "  2. Push to GitLab to trigger the pipeline"
    Write-ColorOutput "  3. Monitor pipeline execution in GitLab UI"
    Write-ColorOutput "  4. Review coverage reports in merge requests"
    Write-ColorOutput ""
    Write-ColorOutput "====================================" "Cyan"
    Write-ColorOutput "✅ All pipeline stages validated successfully!" "Green"

} catch {
    Write-Error "Pipeline validation failed: $($_.Exception.Message)"
    exit 1
}
