#!/bin/bash

# Simple GitLab CI validation script
echo "🔍 Validating GitLab CI configuration..."

# Check if .gitlab-ci.yml exists
if [ ! -f ".gitlab-ci.yml" ]; then
    echo "❌ .gitlab-ci.yml not found"
    exit 1
fi

echo "✅ .gitlab-ci.yml found"

# Check basic structure
echo "📋 Checking pipeline structure..."

# Check stages
if grep -q "stages:" .gitlab-ci.yml; then
    echo "✅ Stages defined"
else
    echo "❌ No stages found"
    exit 1
fi

# Check jobs
if grep -q "build:" .gitlab-ci.yml; then
    echo "✅ Build job found"
else
    echo "❌ Build job not found"
    exit 1
fi

if grep -q "test:" .gitlab-ci.yml; then
    echo "✅ Test job found"
else
    echo "❌ Test job not found"
    exit 1
fi

if grep -q "package:" .gitlab-ci.yml; then
    echo "✅ Package job found"
else
    echo "❌ Package job not found"
    exit 1
fi

# Check for problematic patterns
if grep -q "only:" .gitlab-ci.yml; then
    echo "⚠️  Warning: 'only:' syntax found - consider using 'rules:' instead"
fi

if grep -q "tags:" .gitlab-ci.yml; then
    echo "⚠️  Warning: 'tags:' found - make sure GitLab runners have these tags"
fi

echo "🎉 GitLab CI validation completed!"
echo ""
echo "📝 Pipeline Summary:"
echo "   - 3 stages: build, test, package"
echo "   - All jobs use 'rules: - when: always'"
echo "   - No restrictive branch/tag requirements"
echo "   - Should run on any branch/commit"
echo ""
echo "🚀 Ready to commit and push to GitLab!"
