# Simple GitLab CI/CD Pipeline Validation Script
Write-Host "🚀 GitLab CI/CD Pipeline Validation" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# BUILD STAGE
Write-Host ""
Write-Host "🏗️ BUILD STAGE" -ForegroundColor Blue
Write-Host "Generating OpenAPI code..."
& .\gradlew.bat openApiGenerate --quiet
if ($LASTEXITCODE -ne 0) { exit 1 }

Write-Host "Compiling application..."
& .\gradlew.bat compileJava --quiet
if ($LASTEXITCODE -ne 0) { exit 1 }

Write-Host "✅ Build stage completed!" -ForegroundColor Green

# TEST STAGE
Write-Host ""
Write-Host "🧪 TEST STAGE" -ForegroundColor Blue
Write-Host "Running tests with coverage..."
& .\gradlew.bat testWithCoverage --quiet
if ($LASTEXITCODE -ne 0) { exit 1 }

Write-Host "✅ Test stage completed!" -ForegroundColor Green

# PACKAGE STAGE
Write-Host ""
Write-Host "📦 PACKAGE STAGE" -ForegroundColor Blue
Write-Host "Building JAR..."
& .\gradlew.bat bootJar --quiet
if ($LASTEXITCODE -ne 0) { exit 1 }

$jarFile = Get-ChildItem "build\libs\*.jar" | Select-Object -First 1
if ($jarFile) {
    Write-Host "✅ JAR created: $($jarFile.Name)" -ForegroundColor Green
} else {
    Write-Host "❌ JAR file not found" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Package stage completed!" -ForegroundColor Green

# SUMMARY
Write-Host ""
Write-Host "🎉 All pipeline stages completed successfully!" -ForegroundColor Green
Write-Host "Ready for GitLab CI/CD!" -ForegroundColor Cyan
