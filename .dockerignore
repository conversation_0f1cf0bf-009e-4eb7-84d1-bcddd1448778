# Exclude plain jars (we only want the fat jar)
build/libs/*-plain.jar

# Exclude unnecessary build artifacts
build/reports/
build/test-results/
build/tmp/
build/classes/
build/generated/
build/resources/main/static/
build/resources/main/templates/

# Exclude development files
.gradle/
.idea/
.vscode/
*.iml
*.ipr
*.iws

# Exclude version control
.git/
.gitignore
.gitattributes

# Exclude CI/CD files
.gitlab-ci.yml
.gitlab-ci.yml.wrong

# Exclude documentation
README.md
*.md

# Exclude test data
data/
src/test/

# Exclude other Docker files
Dockerfile.old

# Exclude system files
.DS_Store
Thumbs.db
*~
